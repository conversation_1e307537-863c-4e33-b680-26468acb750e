# LingXia Bridge Communication Specification

## 1. Overview

This document defines the JSON-based communication protocol used for interactions between the **Logic Layer** and the **View Layer** within the Lingxia application architecture. This communication is facilitated by a Native Bridge, which relays messages between the two environments.

*   **Logic Layer:** This corresponds to the concept of an **AppService** in architectures like WeChat Mini Programs. It is where the developer's JavaScript business logic executes, running in a dedicated JavaScript engine (e.g., QuickJS, V8, JSCore) managed by the native application shell, **separate** from the View Layer's environment.
*   **View Layer:** This consists of one or more WebViews responsible for rendering the user interface based on data received from the Logic Layer.

The protocol prioritizes robustness and clear error handling, primarily using a `call`/`reply` mechanism for operations requiring confirmation and an `event` mechanism for simple notifications.

## 2. Core Principles

*   **Bidirectional:** Both Logic Layer and View Layer can initiate communication.
*   **Message-Driven:** Communication occurs via structured JSON messages.
*   **Asynchronous:** Interactions are asynchronous, relying on callbacks and potentially timeouts.
*   **Robustness:** Mandatory acknowledgements (`reply`) for critical operations (`call`) ensure reliable state management and error reporting.

## 3. Message Types

There are four fundamental message types:

1.  **`call`**:
    *   **Purpose:** Initiate a function execution on the receiving layer when the sender *requires* confirmation of receipt and the outcome (success or failure) of the initial processing attempt.
    *   **Response Required:** The receiver **must** send a `reply` message in response.
    *   **`msgId`:** Must contain a unique identifier generated by the sender.
    *   **Timeout:** The sender is responsible for implementing timeout logic if a timely response is critical.

2.  **`reply`**:
    *   **Purpose:** Respond to a previously received `call` message, acknowledging its receipt and reporting the **immediate status** of initiating the requested operation (i.e., whether the corresponding handler was successfully invoked and its initial synchronous execution phase completed without error). It does **not** typically carry the final computed result of the business operation, which is often communicated asynchronously (e.g., via `setData`).
    *   **Response To:** Must only be sent in response to a `call`.
    *   **`msgId`:** Must contain the exact `msgId` from the original `call` message it is responding to.
    *   **Payload:** Contains the result (`success`/`failure`) of the handler invocation attempt (see Section 6).

3.  **`event`**:
    *   **Purpose:** Send a notification to the receiving layer where the sender does *not* strictly require confirmation (fire-and-forget).
    *   **Response Required:** None. The receiver is not obligated to respond.
    *   **`msgId`:** Should be `null`.
    *   **Use Case:** For general-purpose notifications that don't expect or require any specific follow-up action.

4.  **`callback`**:
    *   **Purpose:** Signal completion of an asynchronous operation that was initiated by the receiving layer, typically with a `callbackId` provided in a previous message.
    *   **Response Required:** None. The receiver is not obligated to respond.
    *   **`msgId`:** Should be `null`.
    *   **`callbackId`:** Required. The identifier from the original request that this callback is responding to.
    *   **Use Case:** Primarily for View Layer to notify Logic Layer about the completion of processing operations that were initiated by Logic Layer calls with a callbackId.

## 4. JSON Message Structure

All messages exchanged between the Logic Layer and View Layer via the Native Bridge adhere to the following structure:

```json
{
  "msgId": "<string|null>",
  "type": "call | reply | event | callback",
  "name": "<string>",
  "payload": "<object|null>",
  "callbackId": "<string>"
}
```

**Field Descriptions:**

*   `msgId` (`String | Null`):
    *   A unique identifier for `call` messages, generated by the sender (see Section 5).
    *   Required and mirrored in the corresponding `reply` message.
    *   Should be `null` for `event` and `callback` messages.
*   `type` (`String`):
    *   Specifies the message type: `"call"`, `"reply"`, `"event"`, or `"callback"`.
*   `name` (`String`, *Optional*):
    *   **Required** for `call`: The name of the function to be invoked on the receiving layer (e.g., `"submitForm"`, `"setData"`).
    *   **Required** for `event`: The name describing the event that occurred (e.g., `"userScrolled"`).
    *   **Optional** for `callback`: Can be omitted as the `callbackId` is sufficient for correlation.
    *   **Must be omitted** for `reply` messages.
*   `payload` (`Object | Null`):
    *   For `call`: An object containing the parameters for the function invocation. May include a `callbackId` if the caller expects an asynchronous completion notification.
    *   For `event`: An object containing data relevant to the event.
    *   For `reply`: An object detailing the result of the `call` execution (see Section 6).
    *   **Not used** for `callback` messages.
*   `callbackId` (`String`, *Optional*):
    *   **Required** for `callback`: The identifier from the original request that this callback is responding to.
    *   **Not used** for other message types.

## 5. Message ID Generation (`msgId`)

To ensure reliable correlation between `call` and `reply` messages, a unique `msgId` must be generated by the sender of the `call`.

A recommended format is:

`{layer_prefix}-{timestamp_ms}-{counter}`

*   `layer_prefix`: A short identifier for the sending layer (e.g., `view` or `logic`).
*   `timestamp_ms`: The current Unix timestamp in milliseconds.
*   `counter`: A simple counter that increments for each message sent from that layer and resets periodically or maintains a high-enough range to avoid collisions within the same millisecond.

*Example:* `view-1678886400123-1`, `logic-1678886400567-10`

## 6. Reply Payload Structure

The `payload` of a `reply` message **must** conform to one of the following structures to indicate the outcome of the attempt to invoke the handler specified in the original `call`:

**Success (without result data):**

```json
{
  "success": true
}
```
*Indicates the corresponding `call` was received, the specified handler was found, successfully invoked, and its initial synchronous execution phase completed without raising an immediate error. It implies the operation has been accepted for processing.*
*Note: This format is used for operations that don't need to return data immediately.*

**Success (with result data):**

```json
{
  "success": true,
  "result": "<any>" // OPTIONAL: The actual result data from the handler
}
```
*Indicates successful execution and includes the computed result. The `result` field can contain any JSON-serializable value (object, array, string, number, boolean, null). This format is used for fast operations that need to return data immediately.*

**Failure:**

```json
{
  "success": false,
  "error": {
    "message": "<string>" // REQUIRED: A developer/user-friendly error description
  }
}
```
*Indicates the corresponding `call` could not be successfully processed upon receipt. This could be due to reasons such as: the specified handler `name` was not found, the handler rejected the request due to invalid parameters during initial synchronous validation, or an error occurred during the handler's immediate synchronous execution.*

*Note on Timing: The `reply` message is typically sent by the receiving layer's **bridge framework** immediately after attempting to invoke the business handler, reflecting the status of that invocation attempt. For handlers that return immediate results (fast operations), the `result` field should contain the computed value. For long-running operations, the traditional `success: true` format should be used, with results delivered via subsequent `setData` calls.*

## 7. Communication Flow Examples

*Note: The `msgId` values used below are illustrative examples following the format `{layer_prefix}-{timestamp_ms}-{counter}`.*

### 7.1. View -> Logic (Handler Invocation Requiring Feedback)

*Scenario: User clicks a button triggering complex logic (`submitForm`) in the Logic Layer.*

1.  **View Sends `call`:**
    ```json
    {
      "msgId": "view-1678886400123-1",
      "type": "call",
      "name": "submitForm",
      "payload": { "field1": "value1", "field2": "value2" }
    }
    ```

2.  **Logic Bridge Processes:** Receives the call, finds the `submitForm` handler. Attempts to invoke the handler with the payload. Assume the handler starts its synchronous execution phase without immediate error (e.g., basic parameter validation passes).

3.  **Logic Bridge Responds `reply` (Success - Sent Immediately):**
    ```json
    {
      "msgId": "view-1678886400123-1",
      "type": "reply",
      "payload": { "success": true }
    }
    ```
    *(Meanwhile, the `submitForm` handler in the Logic Layer continues its potentially asynchronous processing).*

4.  **Logic Bridge Responds `reply` (Failure - Sent Immediately):**
    *(Alternative to step 3 if handler invocation failed immediately)*
    ```json
    {
      "msgId": "view-1678886400123-1",
      "type": "reply",
      "payload": {
        "success": false,
        "error": { "message": "Invalid input for field2." }
      }
    }
    ```

### 7.2. Logic -> View (`setData` Implementation)

*Scenario: Logic Layer updates data and needs the View Layer to apply it. An optional callback identifier can be passed for asynchronous notification after the view update is processed.*

1.  **Logic Sends `event`:**
    ```json
    {
      "msgId": null,
      "type": "event",
      "name": "setData",
      "payload": {
        "data": { // The patch object or full data state
          "user.profile.name": "Updated Name",
          "cart.count": 5
        },
        "callbackId": "<String | null>" // OPTIONAL: Identifier for a desired callback
      }
    }
    ```
    *The `callbackId`, if provided, is opaque to the View Layer's immediate `setData` handler but should be relayed if the View Layer itself intends to signal further processing completion.*

2.  **View Bridge Processes (No Reply Required):**
    *The View Layer receives the `setData` event and processes it immediately. Since this is an `event` type message, no reply is sent back to the Logic Layer. This design choice optimizes performance by eliminating the need for synchronous confirmation, as `setData` operations are inherently fire-and-forget data updates.*



### 7.2.1. View -> Logic (Callback for `setData` - OPTIONAL)

*Scenario: After the View Layer has processed a `setData` event that included a `callbackId`, and any relevant internal view updates are considered complete by the View Layer, it can send a callback notification back to the Logic Layer.*

1.  **View Sends `callback`:**
    ```json
    {
      "msgId": null,
      "type": "callback",
      "callbackId": "<String>" 
    }
    ```
    *Logic Layer receives this callback notification. If it has a pending operation associated with this `callbackId`, it can now proceed with it (e.g., invoke a stored JavaScript callback function). The `callbackId` must match one that was previously sent from Logic to View in a `setData` event.*

### 7.3. View -> Logic (Call with Result - Fast Operations)

*Scenario: View Layer needs to retrieve data synchronously from Logic Layer for fast operations.*

1.  **View Sends `call`:**
    ```json
    {
      "msgId": "view-1678886400789-5",
      "type": "call",
      "name": "fastDataRetrieval",
      "payload": { "key": "someKey" }
    }
    ```

2.  **Logic Bridge Processes:** Receives the call, finds the handler, invokes it with the payload, and the handler returns the data immediately (fast execution).

3.  **Logic Bridge Responds `reply` (Success with Result):**
    ```json
    {
      "msgId": "view-1678886400789-5",
      "type": "reply",
      "payload": {
        "success": true,
        "result": {
          "data": "some value",
          "timestamp": 1678886400789
        }
      }
    }
    ```

4.  **Logic Bridge Responds `reply` (Failure):**
    *(Alternative to step 3 if the operation fails)*
    ```json
    {
      "msgId": "view-1678886400789-5",
      "type": "reply",
      "payload": {
        "success": false,
        "error": { "message": "Data not found or operation failed." }
      }
    }
    ```

*Note: This pattern should only be used for fast operations that complete immediately. Time-consuming operations must use the traditional `call` + `setData` pattern to avoid blocking the bridge.*

### 7.4. View -> Logic (Simple Event Notification)

*Scenario: User performs a minor interaction tracked for analytics.*

1.  **View Sends `event`:**
    ```json
    {
      "msgId": null,
      "type": "event",
      "name": "userScrolled",
      "payload": { "elementId": "product-list", "scrollDepth": 0.75 }
    }
    ```
    *No response expected.*

2.  **Logic Processes:** Receives the event and logs analytics data.

## 8. Robustness Considerations

*   **Timeouts:** Senders of `call` messages are responsible for implementing appropriate timeout mechanisms to handle non-responsive receivers.
*   **Error Propagation:** The mandatory `reply` for `call` ensures that execution failures (including errors during `setData` application in the View) are reliably reported back to the caller.
*   **Native Bridge Reliability:** The underlying Native Bridge implementation must reliably route messages between the correct Logic and View instances, especially in multi-page scenarios.
*   **Message Validation:** Both Logic and View layers should perform basic validation on received messages (e.g., check for required fields based on `type`) to prevent errors caused by malformed messages.
*   **`event` and `callback` Messages:** These message types are "fire-and-forget" and do not elicit a `reply`. Senders should not expect acknowledgment for these, and receivers are not obligated to confirm receipt.
