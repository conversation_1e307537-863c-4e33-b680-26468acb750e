[workspace]
members = [
    "lingxia-miniapp",
    "lingxia-webview",
]
resolver = "2"

[workspace.dependencies]
# Common dependencies that can be shared across workspace members
tokio = { version = "1.0", features = ["time", "macros", "sync"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0.140"
http = "1.3.1"
thiserror = "2.0.12"
dashmap = "6.1.0"

# Local dependencies
rong = { path = "../starfire" }
rong_modules = { path = "../starfire/rong_modules", features=["default", "storage"]}

[profile.release]
opt-level = 'z'
lto = true
codegen-units = 1
