declare namespace _default {
    let plugins: any[];
    namespace css {
        namespace postcss {
            let plugins_1: any[];
            export { plugins_1 as plugins };
        }
    }
    namespace build {
        let outDir: string;
        let emptyOutDir: boolean;
        namespace rollupOptions {
            namespace output {
                let entryFileNames: string;
                let assetFileNames: string;
                let inlineDynamicImports: boolean;
            }
        }
        let cssCodeSplit: boolean;
        let assetsInlineLimit: number;
    }
}
export default _default;
//# sourceMappingURL=vite.config.tailwind.d.ts.map