import vue from '@vitejs/plugin-vue';
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';
export default {
    plugins: [vue()],
    css: {
        postcss: {
            plugins: [
                tailwindcss,
                autoprefixer,
            ],
        },
    },
    build: {
        outDir: 'dist',
        emptyOutDir: true,
        rollupOptions: {
            output: {
                entryFileNames: 'main.js',
                assetFileNames: 'assets/[name].[ext]',
                inlineDynamicImports: true
            }
        },
        cssCodeSplit: false,
        assetsInlineLimit: 100000000
    }
};
//# sourceMappingURL=vite.config.tailwind.js.map