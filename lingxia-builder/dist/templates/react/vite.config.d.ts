declare namespace _default {
    let plugins: any[];
    namespace build {
        namespace rollupOptions {
            namespace output {
                let entryFileNames: string;
                let chunkFileNames: string;
                let assetFileNames: string;
            }
        }
        let outDir: string;
        let emptyOutDir: boolean;
        let target: string;
        let minify: boolean;
    }
    namespace esbuild {
        let jsx: string;
    }
    let logLevel: string;
}
export default _default;
//# sourceMappingURL=vite.config.d.ts.map