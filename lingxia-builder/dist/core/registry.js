/**
 * Framework Registry - Centralized framework configuration
 * Makes it easy to add new frameworks without modifying multiple files
 */
/**
 * Centralized registry of all supported frameworks
 */
export class FrameworkRegistry {
    static frameworks = new Map([
        ['react', {
                name: 'React',
                extensions: ['.tsx', '.jsx'],
                mainTemplate: 'main.jsx',
                indexTemplate: 'index.html',
                vitePlugin: '@vitejs/plugin-react',
                hasComponents: true
            }],
        ['vue', {
                name: 'Vue',
                extensions: ['.vue'],
                mainTemplate: 'main.js',
                indexTemplate: 'index.html',
                vitePlugin: '@vitejs/plugin-vue',
                hasComponents: true
            }],
        ['html', {
                name: 'HTML',
                extensions: ['.html'],
                mainTemplate: '',
                indexTemplate: '',
                hasComponents: false
            }]
    ]);
    /**
     * Get all supported frameworks
     */
    static getAllFrameworks() {
        return Array.from(this.frameworks.keys());
    }
    /**
     * Get framework configuration
     */
    static getFramework(name) {
        return this.frameworks.get(name);
    }
    /**
     * Detect framework from file extension
     */
    static detectFramework(filePath) {
        const ext = filePath.substring(filePath.lastIndexOf('.'));
        for (const [frameworkName, config] of this.frameworks) {
            if (config.extensions.includes(ext)) {
                return frameworkName;
            }
        }
        return 'html'; // Default fallback
    }
    /**
     * Check if framework is supported
     */
    static isSupported(framework) {
        return this.frameworks.has(framework);
    }
    /**
     * Register a new framework (for future extensibility)
     */
    static register(name, config) {
        this.frameworks.set(name, config);
    }
    /**
     * Get frameworks that need Vite building
     */
    static getBuildableFrameworks() {
        return Array.from(this.frameworks.entries())
            .filter(([_, config]) => config.hasComponents)
            .map(([name, _]) => name);
    }
}
//# sourceMappingURL=registry.js.map