import type { Page, PageFiles } from '../../types/index.js';
/**
 * Get page title from configuration or default
 */
export declare function getPageTitle(page: Page, pageFiles: PageFiles): string;
/**
 * Detect page type from file extension
 */
export declare function detectPageType(filePath: string): 'html' | 'react' | 'vue';
/**
 * Validate page structure
 */
export declare function validatePageStructure(page: Page, pageFiles: PageFiles): string[];
//# sourceMappingURL=page.d.ts.map