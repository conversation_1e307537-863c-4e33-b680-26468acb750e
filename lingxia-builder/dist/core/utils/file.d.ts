export declare class FileUtils {
    /**
     * Copy directory recursively
     */
    copyDirectory(sourceDir: string, destDir: string): Promise<void>;
    /**
     * Ensure directory exists
     */
    ensureDirectory(dirPath: string): void;
    /**
     * Clean directory (remove all contents)
     */
    cleanDirectory(dirPath: string): void;
    /**
     * Get file extension without dot
     */
    getExtension(filePath: string): string;
    /**
     * Get base name without extension
     */
    getBaseName(filePath: string): string;
    /**
     * Check if file exists and is readable
     */
    isReadableFile(filePath: string): boolean;
    /**
     * Read JSON file safely
     */
    readJsonFile<T = any>(filePath: string): T | null;
    /**
     * Write JSON file with formatting
     */
    writeJsonFile(filePath: string, data: any): void;
}
//# sourceMappingURL=file.d.ts.map