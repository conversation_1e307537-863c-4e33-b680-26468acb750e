{"version": 3, "file": "registry.js", "sourceRoot": "", "sources": ["../../src/core/registry.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAWH;;GAEG;AACH,MAAM,OAAO,iBAAiB;IACpB,MAAM,CAAC,UAAU,GAAiC,IAAI,GAAG,CAAC;QAChE,CAAC,OAAO,EAAE;gBACR,IAAI,EAAE,OAAO;gBACb,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;gBAC5B,YAAY,EAAE,UAAU;gBACxB,aAAa,EAAE,YAAY;gBAC3B,UAAU,EAAE,sBAAsB;gBAClC,aAAa,EAAE,IAAI;aACpB,CAAC;QACF,CAAC,KAAK,EAAE;gBACN,IAAI,EAAE,KAAK;gBACX,UAAU,EAAE,CAAC,MAAM,CAAC;gBACpB,YAAY,EAAE,SAAS;gBACvB,aAAa,EAAE,YAAY;gBAC3B,UAAU,EAAE,oBAAoB;gBAChC,aAAa,EAAE,IAAI;aACpB,CAAC;QACF,CAAC,MAAM,EAAE;gBACP,IAAI,EAAE,MAAM;gBACZ,UAAU,EAAE,CAAC,OAAO,CAAC;gBACrB,YAAY,EAAE,EAAE;gBAChB,aAAa,EAAE,EAAE;gBACjB,aAAa,EAAE,KAAK;aACrB,CAAC;KACH,CAAC,CAAC;IAEH;;OAEG;IACH,MAAM,CAAC,gBAAgB;QACrB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,IAAY;QAC9B,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,QAAgB;QACrC,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAE1D,KAAK,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACtD,IAAI,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACpC,OAAO,aAAa,CAAC;YACvB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC,CAAC,mBAAmB;IACpC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,SAAiB;QAClC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAY,EAAE,MAAuB;QACnD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB;QAC3B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;aACzC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC;aAC7C,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC"}