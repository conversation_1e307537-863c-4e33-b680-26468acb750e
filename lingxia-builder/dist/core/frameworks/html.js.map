{"version": 3, "file": "html.js", "sourceRoot": "", "sources": ["../../../src/core/frameworks/html.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,kBAAkB,EAAE,MAAM,WAAW,CAAC;AAE/C,OAAO,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAE7C;;;GAGG;AACH,MAAM,OAAO,aAAc,SAAQ,kBAAkB;IAC3C,SAAS,CAAY;IAE7B,YAAY,WAAmB,EAAE,SAAiB,EAAE,YAAoB;QACtE,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;QAC5C,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;IACnC,CAAC;IAED,gBAAgB;QACd,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,aAAa;QACX,OAAO,CAAC,OAAO,CAAC,CAAC;IACnB,CAAC;IAED,eAAe;QACb,OAAO;YACL,YAAY,EAAE,EAAE;YAChB,eAAe,EAAE,EAAE;SACpB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,UAAe,EAAE;QACxD,gCAAgC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,UAAU,CACd,QAAgB,EAChB,IAAU,EACV,SAAoB,EACpB,aAAuB;QAEvB,oCAAoC;QACpC,8BAA8B;IAChC,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,IAAU,EACV,SAAoB,EACpB,WAAgC,EAChC,YAAoB;QAEpB,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACzE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAEnE,gCAAgC;QAChC,IAAI,WAAW,GAAG,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEhE,qBAAqB;QACrB,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAChD,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAEhE,8BAA8B;QAC9B,WAAW,GAAG,WAAW,CAAC,OAAO,CAC/B,SAAS,EACT,aAAa,YAAY,sBAAsB,CAChD,CAAC;QAEF,iCAAiC;QACjC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QAE9C,0BAA0B;QAC1B,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,QAAQ,MAAM,CAAC,CAAC;YAClE,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,wBAAwB,QAAQ,MAAM,CAAC,CAAC;QACtD,CAAC;QAED,6BAA6B;QAC7B,IAAI,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,QAAQ,OAAO,CAAC,CAAC;YACtE,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;YACzD,OAAO,CAAC,GAAG,CAAC,2BAA2B,QAAQ,OAAO,CAAC,CAAC;QAC1D,CAAC;QAED,mBAAmB;QACnB,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,QAAQ,OAAO,CAAC,CAAC;QACpE,EAAE,CAAC,aAAa,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,yBAAyB,QAAQ,OAAO,CAAC,CAAC;IACxD,CAAC;IAED;;;OAGG;IACK,oBAAoB,CAAC,WAAmB,EAAE,SAAiB;QACjE,wCAAwC;QACxC,MAAM,UAAU,GAAG,2BAA2B,CAAC;QAC/C,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE9C,IAAI,QAAQ,EAAE,CAAC;YACb,yBAAyB;YACzB,OAAO,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,SAAS,UAAU,CAAC,CAAC;QACxE,CAAC;aAAM,CAAC;YACN,4BAA4B;YAC5B,MAAM,SAAS,GAAG,cAAc,CAAC;YACjC,IAAI,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;gBAChC,OAAO,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,kBAAkB,SAAS,UAAU,CAAC,CAAC;YAC/E,CAAC;iBAAM,CAAC;gBACN,uCAAuC;gBACvC,MAAM,SAAS,GAAG,cAAc,CAAC;gBACjC,IAAI,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;oBAChC,OAAO,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,0BAA0B,SAAS,mBAAmB,CAAC,CAAC;gBAChG,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;CAGF"}