import { FrameworkProcessor } from './base.js';
import type { Page, PageFiles } from '../../types/index.js';
/**
 * React framework processor
 * Uses templates and framework-specific logic
 */
export declare class ReactProcessor extends FrameworkProcessor {
    private fileUtils;
    private templateManager;
    constructor(projectPath: string, outputDir: string, templatesDir: string);
    getFrameworkName(): string;
    getExtensions(): string[];
    getDependencies(): {
        dependencies: any;
        devDependencies: any;
    };
    createViteConfig(buildDir: string, options?: any): Promise<any>;
    setupBuild(buildDir: string, page: Page, pageFiles: PageFiles, pageFunctions: string[]): Promise<void>;
    generateOutput(page: Page, pageFiles: PageFiles, buildResult: {
        distDir: string;
    }, bridgeScript: string): Promise<void>;
    private processTemplates;
    /**
     * Fix HTML paths to use relative paths for React
     */
    private fixHtmlPaths;
}
//# sourceMappingURL=react.d.ts.map