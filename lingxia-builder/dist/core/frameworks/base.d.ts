import type { Page, PageFiles } from '../../types/index.js';
/**
 * Abstract base class for framework processors
 * Each framework implements this interface for specific handling
 */
export declare abstract class FrameworkProcessor {
    protected projectPath: string;
    protected outputDir: string;
    protected templatesDir: string;
    constructor(projectPath: string, outputDir: string, templatesDir: string);
    /**
     * Get framework name
     */
    abstract getFrameworkName(): string;
    /**
     * Get framework-specific file extensions
     */
    abstract getExtensions(): string[];
    /**
     * Create Vite configuration for this framework
     */
    abstract createViteConfig(buildDir: string, options?: any): any;
    /**
     * Setup framework-specific build environment
     */
    abstract setupBuild(buildDir: string, page: Page, pageFiles: PageFiles, pageFunctions: string[]): Promise<void>;
    /**
     * Generate final output for this framework
     */
    abstract generateOutput(page: Page, pageFiles: PageFiles, buildResult: {
        distDir: string;
    }, bridgeScript: string): Promise<void>;
    /**
     * Get package.json dependencies for this framework
     */
    abstract getDependencies(): {
        dependencies: any;
        devDependencies: any;
    };
    /**
     * Process page title in framework-specific way
     */
    protected processPageTitle(content: string, pageTitle: string): string;
    /**
     * Copy framework templates to build directory
     */
    protected copyTemplates(buildDir: string): void;
    /**
     * Load framework-specific template function
     */
    protected loadTemplateFunction(functionName: string): Promise<any>;
}
//# sourceMappingURL=base.d.ts.map