{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../../src/core/frameworks/base.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAG7B;;;GAGG;AACH,MAAM,OAAgB,kBAAkB;IAC5B,WAAW,CAAS;IACpB,SAAS,CAAS;IAClB,YAAY,CAAS;IAE/B,YAAY,WAAmB,EAAE,SAAiB,EAAE,YAAoB;QACtE,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACnC,CAAC;IA0CD;;OAEG;IACO,gBAAgB,CAAC,OAAe,EAAE,SAAiB;QAC3D,6CAA6C;QAC7C,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,kBAAkB,IAAI,CAAC,gBAAgB,EAAE,eAAe,EAAE,GAAG,CAAC,CAAC;QAC/F,OAAO,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,SAAS,UAAU,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACO,aAAa,CAAC,QAAgB;QACtC,MAAM,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;QAEjG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,kCAAkC,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QAC/E,CAAC;QAED,MAAM,aAAa,GAAG,EAAE,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;QAC3D,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;YACjC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;YACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAE3C,IAAI,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,IAAI,IAAI,KAAK,gBAAgB,EAAE,CAAC;gBAClE,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,oBAAoB,CAAC,YAAoB;QACvD,MAAM,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;QACjG,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,gBAAgB,CAAC,CAAC;QAErE,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,UAAU,UAAU,EAAE,CAAC,CAAC;YACpD,OAAO,MAAM,CAAC,YAAY,CAAC,CAAC;QAC9B,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,qBAAqB,YAAY,kBAAkB,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;IAChG,CAAC;CACF"}