import { FrameworkProcessor } from './base.js';
import type { Page, PageFiles } from '../../types/index.js';
/**
 * HTML framework processor
 * Handles static HTML pages
 */
export declare class HtmlProcessor extends FrameworkProcessor {
    private fileUtils;
    constructor(projectPath: string, outputDir: string, templatesDir: string);
    getFrameworkName(): string;
    getExtensions(): string[];
    getDependencies(): {
        dependencies: any;
        devDependencies: any;
    };
    createViteConfig(buildDir: string, options?: any): Promise<any>;
    setupBuild(buildDir: string, page: Page, pageFiles: PageFiles, pageFunctions: string[]): Promise<void>;
    generateOutput(page: Page, pageFiles: PageFiles, buildResult: {
        distDir: string;
    }, bridgeScript: string): Promise<void>;
    /**
     * Process HTML page title based on page configuration
     * Handles both cases: existing title and no title
     */
    private processHtmlPageTitle;
}
//# sourceMappingURL=html.d.ts.map