import { FrameworkProcessor } from './base.js';
/**
 * Framework Factory - Creates appropriate framework processor
 * Adding new frameworks only requires adding them here
 */
export declare class FrameworkFactory {
    private static processors;
    /**
     * Create framework processor for given framework
     */
    static createProcessor(framework: string, projectPath: string, outputDir: string, templatesDir: string): FrameworkProcessor;
    /**
     * Detect framework from file path
     */
    static detectFramework(filePath: string): string;
    /**
     * Get all supported frameworks
     */
    static getSupportedFrameworks(): string[];
    /**
     * Register a new framework processor
     */
    static registerFramework(name: string, processorClass: new (projectPath: string, outputDir: string, templatesDir: string) => FrameworkProcessor): void;
    /**
     * Check if framework is supported
     */
    static isSupported(framework: string): boolean;
}
//# sourceMappingURL=factory.d.ts.map