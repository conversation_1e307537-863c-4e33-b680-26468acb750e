{"version": 3, "file": "react.js", "sourceRoot": "", "sources": ["../../../src/core/frameworks/react.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,kBAAkB,EAAE,MAAM,WAAW,CAAC;AAE/C,OAAO,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAC7C,OAAO,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AAEjD;;;GAGG;AACH,MAAM,OAAO,cAAe,SAAQ,kBAAkB;IAC5C,SAAS,CAAY;IACrB,eAAe,CAAkB;IAEzC,YAAY,WAAmB,EAAE,SAAiB,EAAE,YAAoB;QACtE,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;QAC5C,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;QACjC,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;IAC/C,CAAC;IAED,gBAAgB;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,aAAa;QACX,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;IAChE,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,UAAe,EAAE;QACxD,mDAAmD;QACnD,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,IAAI,KAAK,CAAC;QACrC,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QACzF,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QACvD,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC,CAAC,oDAAoD;IACnE,CAAC;IAED,KAAK,CAAC,UAAU,CACd,QAAgB,EAChB,IAAU,EACV,SAAoB,EACpB,aAAuB;QAEvB,sCAAsC;QACtC,IAAI,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAE5D,wDAAwD;QACxD,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;QAC1D,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,4BAA4B,EAAE,gBAAgB,CAAC,CAAC;QAE1E,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;QAE1D,2BAA2B;QAC3B,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAE7B,4CAA4C;QAC5C,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;IACxE,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,IAAU,EACV,SAAoB,EACpB,WAAgC,EAChC,YAAoB;QAEpB,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACzE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAEnE,oBAAoB;QACpB,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QACpE,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC9D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAE1D,IAAI,WAAW,GAAG,EAAE,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAE3D,iCAAiC;QACjC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QAE9C,gEAAgE;QAChE,IAAI,eAAe,GAAG,EAAE,CAAC;QAEzB,4CAA4C;QAC5C,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAC3B,MAAM,WAAW,GAAG,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACnE,eAAe,IAAI,WAAW,GAAG,MAAM,CAAC;QAC1C,CAAC;QAED,+BAA+B;QAC/B,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;YAC1E,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBAC3E,eAAe,IAAI,OAAO,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,IAAI,eAAe,CAAC,IAAI,EAAE,EAAE,CAAC;YAC3B,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,QAAQ,MAAM,CAAC,EAAE,eAAe,CAAC,CAAC;YAC/E,OAAO,CAAC,GAAG,CAAC,wBAAwB,QAAQ,MAAM,CAAC,CAAC;QACtD,CAAC;QAED,aAAa;QACb,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YAC/B,EAAE,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC;YAClE,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC7C,CAAC;QAED,mBAAmB;QACnB,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;YACrB,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,QAAQ,OAAO,CAAC,CAAC;YACtE,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;YACzD,OAAO,CAAC,GAAG,CAAC,2BAA2B,QAAQ,OAAO,CAAC,CAAC;QAC1D,CAAC;QAED,0CAA0C;QAC1C,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QACvD,WAAW,GAAG,WAAW,CAAC,OAAO,CAC/B,SAAS,EACT,aAAa,YAAY,sBAAsB,CAChD,CAAC;QAEF,6BAA6B;QAC7B,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,QAAQ,MAAM,CAAC,CAAC;QACxE,EAAE,CAAC,aAAa,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,gCAAgC,QAAQ,MAAM,CAAC,CAAC;IAC9D,CAAC;IAIO,KAAK,CAAC,gBAAgB,CAC5B,QAAgB,EAChB,IAAU,EACV,SAAoB,EACpB,aAAuB;QAEvB,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAEhD,qBAAqB;QACrB,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QACxD,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;YACjC,IAAI,SAAS,GAAG,EAAE,CAAC,YAAY,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YACxD,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YACxD,EAAE,CAAC,aAAa,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QAC7C,CAAC;QAED,mBAAmB;QACnB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACpD,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YAC/B,IAAI,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAEpD,wBAAwB;YACxB,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;YAChF,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,0BAA0B,EAAE,YAAY,CAAC,CAAC;YAEpE,qCAAqC;YACrC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,6BAA6B,EAAE,6BAA6B,CAAC,CAAC;YAExF,EAAE,CAAC,aAAa,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,WAAmB,EAAE,QAAgB;QACxD,IAAI,YAAY,GAAG,WAAW,CAAC;QAE/B,wEAAwE;QACxE,YAAY,GAAG,YAAY,CAAC,OAAO,CACjC,+CAA+C,EAC/C,iDAAiD,CAClD,CAAC;QAEF,4CAA4C;QAC5C,YAAY,GAAG,YAAY,CAAC,OAAO,CACjC,8CAA8C,EAC9C,kCAAkC,QAAQ,QAAQ,CACnD,CAAC;QAEF,OAAO,YAAY,CAAC;IACtB,CAAC;CACF"}