import * as fs from 'fs';
import * as path from 'path';
/**
 * Abstract base class for framework processors
 * Each framework implements this interface for specific handling
 */
export class FrameworkProcessor {
    projectPath;
    outputDir;
    templatesDir;
    constructor(projectPath, outputDir, templatesDir) {
        this.projectPath = projectPath;
        this.outputDir = outputDir;
        this.templatesDir = templatesDir;
    }
    /**
     * Process page title in framework-specific way
     */
    processPageTitle(content, pageTitle) {
        // Default implementation - can be overridden
        const titlePattern = new RegExp(`<title>LingXia ${this.getFrameworkName()} Page</title>`, 'i');
        return content.replace(titlePattern, `<title>${pageTitle}</title>`);
    }
    /**
     * Copy framework templates to build directory
     */
    copyTemplates(buildDir) {
        const frameworkTemplateDir = path.join(this.templatesDir, this.getFrameworkName().toLowerCase());
        if (!fs.existsSync(frameworkTemplateDir)) {
            throw new Error(`Framework templates not found: ${this.getFrameworkName()}`);
        }
        const templateFiles = fs.readdirSync(frameworkTemplateDir);
        for (const file of templateFiles) {
            const sourcePath = path.join(frameworkTemplateDir, file);
            const destPath = path.join(buildDir, file);
            if (fs.statSync(sourcePath).isFile() && file !== 'vite.config.js') {
                fs.copyFileSync(sourcePath, destPath);
            }
        }
    }
    /**
     * Load framework-specific template function
     */
    async loadTemplateFunction(functionName) {
        const frameworkTemplateDir = path.join(this.templatesDir, this.getFrameworkName().toLowerCase());
        const configPath = path.join(frameworkTemplateDir, 'vite.config.js');
        if (fs.existsSync(configPath)) {
            const module = await import(`file://${configPath}`);
            return module[functionName];
        }
        throw new Error(`Template function ${functionName} not found for ${this.getFrameworkName()}`);
    }
}
//# sourceMappingURL=base.js.map