import type { Page, PageFiles, BuildOptions } from '../../types/index.js';
export declare class PageProcessor {
    private projectPath;
    private outputDir;
    private fileUtils;
    private dependencyResolver;
    private templateManager;
    constructor(projectPath: string, outputDir: string);
    /**
     * Build page using framework-specific processor
     */
    buildPage(page: Page, pageFiles: PageFiles, pageFunctions: string[], options?: BuildOptions): Promise<void>;
    /**
     * Build framework page using Vite
     */
    private buildFrameworkPage;
    private copySrcDirectory;
    private copyTailwindConfig;
    private copyTailwindCss;
}
//# sourceMappingURL=page.d.ts.map