import type { BuildOptions } from '../../types/index.js';
/**
 * Modern LogicBuilder that leverages Vite for dependency resolution and bundling
 */
export declare class LogicBuilder {
    private projectPath;
    private outputDir;
    private fileUtils;
    private templateManager;
    private configManager;
    constructor(projectPath: string, outputDir: string);
    buildLogic(options?: BuildOptions): Promise<void>;
    /**
     * Discover logic files based on pages configuration
     */
    private discoverLogicFiles;
    /**
     * Build logic layer using Vite for proper dependency resolution
     */
    private buildLogicWithVite;
    /**
     * Create entry file that imports all logic files
     */
    private createLogicEntry;
    /**
     * Process logic file to add path parameter to Page calls
     */
    private processLogicFileForPath;
    /**
     * Get page path from pages configuration
     */
    private getPagePathFromConfig;
    /**
     * Create Vite config for logic build using TemplateManager
     */
    private createLogicViteConfig;
    /**
     * Copy source files to build directory
     */
    private copySourceFiles;
}
//# sourceMappingURL=logic.d.ts.map