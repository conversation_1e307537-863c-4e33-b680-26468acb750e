import type { Page, BuildOptions } from '../../types/index.js';
export declare class ViewBuilder {
    private projectPath;
    private outputDir;
    private fileUtils;
    private dependencyResolver;
    private pageProcessor;
    constructor(projectPath: string, outputDir: string);
    buildPages(pages: Page[], options?: BuildOptions): Promise<void>;
    private buildPage;
    private detectPageFiles;
    /**
     * Find logic file (.ts or .js) for a page
     */
    private findLogicFile;
    private extractPageFunctions;
    private copyStaticAssets;
    private copyRootFiles;
    private processLxappCss;
    private resolveCssImports;
}
//# sourceMappingURL=view.d.ts.map