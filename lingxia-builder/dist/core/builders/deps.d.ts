import type { DependencyConfig } from '../../types/index.js';
export declare class DependencyResolver {
    private projectPath;
    private fileUtils;
    private templateManager;
    constructor(projectPath: string);
    /**
     * Get dependency configuration for framework
     */
    getDependencyConfig(): DependencyConfig;
    /**
     * Create package.json for framework with project dependencies
     */
    createPackageJson(framework: string, buildDir: string): void;
    /**
     * Resolve project dependencies in source code
     */
    resolveProjectDependencies(content: string, buildDir: string): Promise<string>;
    /**
     * Analyze HTML dependencies and copy them
     */
    analyzeHtmlDependencies(htmlContent: string, pageDir: string, outputDir: string): Promise<void>;
    private copyLocalResource;
}
//# sourceMappingURL=deps.d.ts.map