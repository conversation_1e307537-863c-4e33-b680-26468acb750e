{"version": 3, "file": "deps.js", "sourceRoot": "", "sources": ["../../../src/core/builders/deps.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,IAAI,MAAM,MAAM,CAAC;AAExB,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAC7C,OAAO,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AAEjD,MAAM,OAAO,kBAAkB;IACrB,WAAW,CAAS;IACpB,SAAS,CAAY;IACrB,eAAe,CAAkB;IAEzC,YAAY,WAAmB;QAC7B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;QACjC,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,eAAe,CAAC,mBAAmB,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,SAAiB,EAAE,QAAgB;QACnD,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;QAE/E,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QACvE,IAAI,WAAW,GAAQ;YACrB,IAAI,EAAE,WAAW,SAAS,OAAO;YACjC,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,QAAQ;SACf,CAAC;QAEF,IAAI,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACtC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;YACpE,IAAI,WAAW,EAAE,CAAC;gBAChB,WAAW,GAAG;oBACZ,GAAG,WAAW;oBACd,YAAY,EAAE,EAAE,GAAG,CAAC,aAAa,CAAC,YAAY,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,CAAC,EAAE;oBAC5F,eAAe,EAAE,EAAE,GAAG,CAAC,aAAa,CAAC,eAAe,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,WAAW,CAAC,eAAe,IAAI,EAAE,CAAC,EAAE;iBACtG,CAAC;gBACF,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,WAAW,GAAG;gBACZ,GAAG,WAAW;gBACd,YAAY,EAAE,aAAa,CAAC,YAAY,IAAI,EAAE;gBAC9C,eAAe,EAAE,aAAa,CAAC,eAAe,IAAI,EAAE;aACrD,CAAC;YACF,OAAO,CAAC,GAAG,CAAC,kBAAkB,SAAS,eAAe,CAAC,CAAC;QAC1D,CAAC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QAC9D,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAAC,OAAe,EAAE,QAAgB;QAChE,MAAM,WAAW,GAAG,8CAA8C,CAAC;QACnE,IAAI,KAA6B,CAAC;QAClC,IAAI,eAAe,GAAG,OAAO,CAAC;QAE9B,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACpD,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAE7D,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gBAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBAE/C,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;gBAEtC,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC3B,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,YAAY,EAAE,EAAE,KAAK,QAAQ,EAAE,CAAC,CAAC;gBAC9E,eAAe,GAAG,eAAe,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBAEhE,OAAO,CAAC,GAAG,CAAC,yBAAyB,YAAY,MAAM,QAAQ,EAAE,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,WAAmB,EAAE,OAAe,EAAE,SAAiB;QACnF,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAEpE,kCAAkC;QAClC,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,wCAAwC,CAAC,IAAI,EAAE,CAAC;QACtF,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YACzD,IAAI,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9B,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC1B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;oBACvD,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,YAAoB,EAAE,SAAiB,EAAE,SAAiB;QACxF,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QACzD,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAChD,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEtC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAClD,OAAO,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,WAAW,EAAE,gBAAgB,QAAQ,EAAE,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;CACF"}