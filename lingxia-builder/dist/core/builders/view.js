import fs from 'fs';
import path from 'path';
import { FileUtils } from '../utils/file.js';
import { DependencyResolver } from './deps.js';
import { PageProcessor } from './page.js';
export class ViewBuilder {
    projectPath;
    outputDir;
    fileUtils;
    dependencyResolver;
    pageProcessor;
    constructor(projectPath, outputDir) {
        this.projectPath = projectPath;
        this.outputDir = outputDir;
        this.fileUtils = new FileUtils();
        this.dependencyResolver = new DependencyResolver(projectPath);
        this.pageProcessor = new PageProcessor(projectPath, outputDir);
    }
    async buildPages(pages, options = {}) {
        console.log(' Building pages...');
        await this.copyStaticAssets();
        await this.copyRootFiles();
        for (const page of pages) {
            await this.buildPage(page, options);
        }
    }
    async buildPage(page, options = {}) {
        const pageFiles = this.detectPageFiles(page);
        // Validate page files exist
        if (!pageFiles.view.exists) {
            throw new Error(`View file not found for page: ${page.path}`);
        }
        const pageFunctions = this.extractPageFunctions(pageFiles);
        // Delegate to PageProcessor for actual building
        await this.pageProcessor.buildPage(page, pageFiles, pageFunctions, options);
    }
    detectPageFiles(page) {
        const pageDir = path.dirname(page.path);
        const baseName = path.basename(page.path, path.extname(page.path));
        const sourcePageDir = path.join(this.projectPath, pageDir);
        // The view file is the page file itself (use actual path from lxapp.json)
        const viewPath = path.join(this.projectPath, page.path);
        const viewExists = fs.existsSync(viewPath);
        return {
            view: {
                path: viewPath,
                exists: viewExists,
                type: page.type
            },
            logic: {
                path: this.findLogicFile(sourcePageDir, baseName),
                exists: this.findLogicFile(sourcePageDir, baseName) !== null
            },
            config: {
                path: path.join(sourcePageDir, `${baseName}.json`),
                exists: fs.existsSync(path.join(sourcePageDir, `${baseName}.json`))
            },
            style: {
                path: path.join(sourcePageDir, `${baseName}.css`),
                exists: fs.existsSync(path.join(sourcePageDir, `${baseName}.css`))
            }
        };
    }
    /**
     * Find logic file (.ts or .js) for a page
     */
    findLogicFile(sourcePageDir, baseName) {
        const tsPath = path.join(sourcePageDir, `${baseName}.ts`);
        const jsPath = path.join(sourcePageDir, `${baseName}.js`);
        if (fs.existsSync(tsPath)) {
            return tsPath;
        }
        else if (fs.existsSync(jsPath)) {
            return jsPath;
        }
        return null;
    }
    extractPageFunctions(pageFiles) {
        if (!pageFiles.logic.exists || !pageFiles.logic.path) {
            return [];
        }
        try {
            const logicContent = fs.readFileSync(pageFiles.logic.path, 'utf-8');
            const functions = [];
            // Find Page({ ... }) call and extract functions from its parameter object
            const pageCallRegex = /Page\s*\(\s*\{([\s\S]*)\}\s*\)/;
            const pageMatch = pageCallRegex.exec(logicContent);
            if (pageMatch) {
                const pageObjectContent = pageMatch[1];
                // Extract function properties from the Page object
                // Matches: functionName: function() {}, functionName: async function() {}
                const functionPropertyRegex = /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*(?:async\s+)?function/g;
                let match;
                while ((match = functionPropertyRegex.exec(pageObjectContent)) !== null) {
                    const functionName = match[1];
                    if (functionName && !functionName.startsWith('_')) {
                        functions.push(functionName);
                    }
                }
            }
            // Filter out lifecycle functions - these are handled by the runtime
            const lifecycleFunctions = [
                'onLoad', 'onShow', 'onHide', 'onUnload', 'onReady',
            ];
            const bridgeFunctions = functions.filter(func => !lifecycleFunctions.includes(func));
            return [...new Set(bridgeFunctions)]; // Remove duplicates
        }
        catch (error) {
            console.warn(`⚠️ Failed to extract functions from ${pageFiles.logic.path}`);
            return [];
        }
    }
    async copyStaticAssets() {
        const staticDirs = ['images', 'assets', 'static'];
        for (const dirName of staticDirs) {
            const sourceDir = path.join(this.projectPath, dirName);
            if (fs.existsSync(sourceDir)) {
                const destDir = path.join(this.outputDir, dirName);
                await this.fileUtils.copyDirectory(sourceDir, destDir);
                console.log(` Copied ${dirName} to dist`);
            }
        }
    }
    async copyRootFiles() {
        // Copy lxapp.json
        const lxappJson = path.join(this.projectPath, 'lxapp.json');
        if (fs.existsSync(lxappJson)) {
            const destFile = path.join(this.outputDir, 'lxapp.json');
            fs.copyFileSync(lxappJson, destFile);
            console.log(` Copied lxapp.json to dist`);
        }
        // Process lxapp.css with import resolution
        const lxappCss = path.join(this.projectPath, 'lxapp.css');
        if (fs.existsSync(lxappCss)) {
            await this.processLxappCss(lxappCss);
        }
    }
    async processLxappCss(cssPath) {
        console.log(` Processing lxapp.css with import resolution...`);
        const finalCss = await this.resolveCssImports(cssPath, new Set());
        const destFile = path.join(this.outputDir, 'lxapp.css');
        fs.writeFileSync(destFile, finalCss);
        console.log(` Generated final lxapp.css to dist`);
    }
    async resolveCssImports(cssPath, processedFiles) {
        const absolutePath = path.resolve(cssPath);
        if (processedFiles.has(absolutePath)) {
            console.warn(`⚠️ Circular import detected: ${cssPath}`);
            return '';
        }
        processedFiles.add(absolutePath);
        if (!fs.existsSync(cssPath)) {
            console.warn(`⚠️ CSS file not found: ${cssPath}`);
            return '';
        }
        const cssContent = fs.readFileSync(cssPath, 'utf-8');
        const cssDir = path.dirname(cssPath);
        let resolvedCss = '';
        const lines = cssContent.split('\n');
        for (const line of lines) {
            const trimmedLine = line.trim();
            const importMatch = trimmedLine.match(/^@import\s+['"]([^'"]+)['"];?/);
            if (importMatch) {
                const importPath = importMatch[1];
                let resolvedPath;
                if (importPath.startsWith('./') || importPath.startsWith('../')) {
                    resolvedPath = path.resolve(cssDir, importPath);
                }
                else if (!importPath.startsWith('http') && !importPath.startsWith('//')) {
                    resolvedPath = path.resolve(this.projectPath, importPath);
                }
                else {
                    resolvedCss += line + '\n';
                    continue;
                }
                const importedCss = await this.resolveCssImports(resolvedPath, processedFiles);
                if (importedCss) {
                    resolvedCss += `/* Imported from: ${importPath} */\n`;
                    resolvedCss += importedCss + '\n';
                }
            }
            else {
                resolvedCss += line + '\n';
            }
        }
        return resolvedCss;
    }
}
//# sourceMappingURL=view.js.map