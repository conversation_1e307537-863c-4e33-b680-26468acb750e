{"version": 3, "file": "page.js", "sourceRoot": "", "sources": ["../../../src/core/builders/page.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AAEzC,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAC7C,OAAO,EAAE,kBAAkB,EAAE,MAAM,WAAW,CAAC;AAE/C,OAAO,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AACjD,OAAO,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAE5D,MAAM,OAAO,aAAa;IAChB,WAAW,CAAS;IACpB,SAAS,CAAS;IAClB,SAAS,CAAY;IACrB,kBAAkB,CAAqB;IACvC,eAAe,CAAkB;IAEzC,YAAY,WAAmB,EAAE,SAAiB;QAChD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;QACjC,IAAI,CAAC,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAC9D,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,IAAU,EAAE,SAAoB,EAAE,aAAuB,EAAE,UAAwB,EAAE;QACnG,kCAAkC;QAClC,MAAM,SAAS,GAAG,gBAAgB,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAExE,6BAA6B;QAC7B,MAAM,SAAS,GAAG,gBAAgB,CAAC,eAAe,CAChD,SAAS,EACT,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,CACvC,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,aAAa,aAAa,CAAC,MAAM,eAAe,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAExF,IAAI,SAAS,KAAK,MAAM,EAAE,CAAC;YACzB,iEAAiE;YACjE,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;YAChF,MAAM,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC;QACjF,CAAC;aAAM,CAAC;YACN,kCAAkC;YAClC,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QACpF,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,SAAc,EACd,IAAU,EACV,SAAoB,EACpB,aAAuB,EACvB,UAAwB,EAAE;QAE1B,MAAM,SAAS,GAAG,SAAS,CAAC,gBAAgB,EAAE,CAAC,WAAW,EAAE,CAAC;QAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAExF,kCAAkC;QAClC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAExC,uBAAuB;QACvB,OAAO,CAAC,GAAG,CAAC,eAAe,SAAS,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC;QAC3E,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAE/D,oCAAoC;QACpC,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QAC5D,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;QACjE,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,OAAO,GAAG;gBACpB,KAAK,EAAE,YAAY;gBACnB,GAAG,EAAE,MAAM;aACZ,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;QAC7D,CAAC;QAED,8CAA8C;QAC9C,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACtC,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACxC,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAErC,iCAAiC;QACjC,MAAM,SAAS,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;QAErE,kEAAkE;QAClE,MAAM,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAEpD,oBAAoB;QACpB,QAAQ,CAAC,aAAa,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,gBAAgB,EAAE,yBAAyB,CAAC,CAAC;QAEtE,OAAO,CAAC,GAAG,CAAC,aAAa,SAAS,CAAC,gBAAgB,EAAE,oBAAoB,CAAC,CAAC;QAC3E,QAAQ,CAAC,eAAe,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;QAE/D,+DAA+D;QAC/D,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;QAEhF,4CAA4C;QAC5C,MAAM,WAAW,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC;QAC7D,MAAM,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IAC7E,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAClD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAE9C,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACvD,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,QAAgB;QAC/C,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC;QAC7E,IAAI,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACtC,IAAI,aAAa,GAAG,EAAE,CAAC,YAAY,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;YAEjE,qDAAqD;YACrD,aAAa,GAAG,aAAa,CAAC,OAAO,CACnC,yBAAyB,EACzB;;;;;;;IAOJ,CACG,CAAC;YAEF,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAC;YACjE,EAAE,CAAC,aAAa,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAAgB;QAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QACpE,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QAExD,IAAI,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;YACnC,EAAE,CAAC,YAAY,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;YAC9C,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;CACF"}