/**
 * Manages all template operations for the LingXia builder
 * Handles template copying, processing, and framework-specific configurations
 */
export declare class TemplateManager {
    private templatesDir;
    constructor();
    /**
     * Get templates directory path
     */
    getTemplatesDir(): string;
    /**
     * Copy framework-specific templates to build directory
     */
    copyFrameworkTemplates(framework: string, buildDir: string): void;
    /**
     * Get appropriate Vite config based on project setup and build mode
     */
    getViteConfig(framework: string, projectPath: string, isProd?: boolean): string;
    /**
     * Get framework dependencies from template
     */
    getFrameworkDependencies(framework: string): any;
    /**
     * Get complete dependency configuration
     */
    getDependencyConfig(): any;
    /**
     * Generate page template with injected functions
     */
    generatePageTemplate(framework: 'react' | 'vue', pageFunctions: string[]): string;
    /**
     * Create package.json for framework builds
     */
    createPackageJson(framework: string, buildDir: string, projectPath: string): void;
    /**
     * Generate function bridge code for page templates
     * Note: Functions are already filtered in ViewBuilder.extractPageFunctions
     */
    generateFunctionBridge(functions: string[]): string;
    /**
     * Check if template exists for framework
     */
    hasFrameworkTemplate(framework: string): boolean;
}
//# sourceMappingURL=template.d.ts.map