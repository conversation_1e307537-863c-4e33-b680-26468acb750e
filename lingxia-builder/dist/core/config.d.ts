/**
 * Centralized configuration manager for LingXia projects
 * Handles lxapp.json and other configuration files
 */
export declare class ConfigManager {
    private projectPath;
    private lxappConfig;
    constructor(projectPath: string);
    /**
     * Read and cache lxapp.json configuration
     */
    getLxappConfig(): any;
    /**
     * Get pages configuration from lxapp.json
     */
    getPages(): string[];
    /**
     * Check if project has package.json
     */
    hasPackageJson(): boolean;
    /**
     * Read package.json if exists
     */
    getPackageJson(): any | null;
    /**
     * Check if project has Tailwind config
     */
    hasTailwindConfig(): boolean;
}
//# sourceMappingURL=config.d.ts.map