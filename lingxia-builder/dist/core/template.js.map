{"version": 3, "file": "template.js", "sourceRoot": "", "sources": ["../../src/core/template.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,aAAa,EAAE,MAAM,KAAK,CAAC;AACpC,OAAO,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAElD,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAE3C;;;GAGG;AACH,MAAM,OAAO,eAAe;IAClB,YAAY,CAAS;IAE7B;QACE,2CAA2C;QAC3C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,qBAAqB,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,SAAiB,EAAE,QAAgB;QACxD,qBAAqB;QACrB,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,0BAA0B,SAAS,EAAE,CAAC,CAAC;QACzD,CAAC;QACD,MAAM,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAErE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,kCAAkC,SAAS,EAAE,CAAC,CAAC;QACjE,CAAC;QAED,0BAA0B;QAC1B,MAAM,aAAa,GAAG,EAAE,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;QAC3D,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;YACjC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;YACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAE3C,IAAI,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC;gBACrC,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,SAAiB,EAAE,WAAmB,EAAE,SAAkB,KAAK;QAC3E,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;YAC1B,oCAAoC;YACpC,OAAO;;;;;;;;;;cAUC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;iBACtB,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM;;GAEvC,CAAC;QACA,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,0BAA0B,SAAS,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,WAAW,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC,CAAC;QAChF,MAAM,cAAc,GAAG,WAAW,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,gBAAgB,CAAC;QAClF,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;QAE3E,IAAI,aAAqB,CAAC;QAC1B,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/B,2BAA2B;YAC3B,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAC;YAClF,aAAa,GAAG,EAAE,CAAC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACvD,CAAC;QAED,iCAAiC;QACjC,IAAI,MAAM,EAAE,CAAC;YACX,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;YAC1E,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;YAChF,2CAA2C;YAC3C,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBACvC,aAAa,GAAG,aAAa,CAAC,OAAO,CACnC,YAAY,EACZ;;sBAEY,CACb,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,SAAiB;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC;QACnE,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;QAEpE,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;YAC1B,OAAO,EAAE,eAAe,EAAE,YAAY,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;QAClE,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,0BAA0B,SAAS,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,aAAa,GAAG,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAE/D,yDAAyD;QACzD,OAAO;YACL,YAAY,EAAE,aAAa,CAAC,YAAY,IAAI,EAAE;YAC9C,eAAe,EAAE;gBACf,GAAG,YAAY,CAAC,MAAM,CAAC,eAAe;gBACtC,GAAG,aAAa,CAAC,eAAe;aACjC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,SAA0B,EAAE,aAAuB;QACtE,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QACzE,IAAI,QAAQ,GAAG,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAEtD,wBAAwB;QACxB,MAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;QAClE,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,oBAAoB,EAAE,cAAc,CAAC,CAAC;QAElE,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,SAAiB,EAAE,QAAgB,EAAE,WAAmB;QACxE,MAAM,eAAe,GAAQ;YAC3B,IAAI,EAAE,WAAW,SAAS,QAAQ;YAClC,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE;gBACP,KAAK,EAAE,YAAY;aACpB;SACF,CAAC;QAEF,sCAAsC;QACtC,MAAM,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;QAE/D,oCAAoC;QACpC,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC;YAC/B,eAAe,CAAC,YAAY,GAAG,EAAE,GAAG,aAAa,CAAC,YAAY,EAAE,CAAC;QACnE,CAAC;QACD,IAAI,aAAa,CAAC,eAAe,EAAE,CAAC;YAClC,eAAe,CAAC,eAAe,GAAG,aAAa,CAAC,eAAe,CAAC;QAClE,CAAC;QAED,kFAAkF;QAClF,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAClE,IAAI,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACtC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC,CAAC;YAC7E,IAAI,WAAW,CAAC,YAAY,EAAE,CAAC;gBAC7B,eAAe,CAAC,YAAY,GAAG;oBAC7B,GAAG,eAAe,CAAC,YAAY;oBAC/B,GAAG,WAAW,CAAC,YAAY;iBAC5B,CAAC;YACJ,CAAC;QACH,CAAC;QAED,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IAClG,CAAC;IAED;;;OAGG;IACH,sBAAsB,CAAC,SAAmB;QACxC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,OAAO,+BAA+B,CAAC;QACzC,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAE/C,OAAO,6BAA6B,YAAY;;;;;;;IAOhD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,SAAiB;QACpC,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAC7D,OAAO,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;IACrC,CAAC;CACF"}