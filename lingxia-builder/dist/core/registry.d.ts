/**
 * Framework Registry - Centralized framework configuration
 * Makes it easy to add new frameworks without modifying multiple files
 */
export interface FrameworkConfig {
    name: string;
    extensions: string[];
    mainTemplate: string;
    indexTemplate: string;
    vitePlugin?: string;
    hasComponents: boolean;
}
/**
 * Centralized registry of all supported frameworks
 */
export declare class FrameworkRegistry {
    private static frameworks;
    /**
     * Get all supported frameworks
     */
    static getAllFrameworks(): string[];
    /**
     * Get framework configuration
     */
    static getFramework(name: string): FrameworkConfig | undefined;
    /**
     * Detect framework from file extension
     */
    static detectFramework(filePath: string): string;
    /**
     * Check if framework is supported
     */
    static isSupported(framework: string): boolean;
    /**
     * Register a new framework (for future extensibility)
     */
    static register(name: string, config: FrameworkConfig): void;
    /**
     * Get frameworks that need Vite building
     */
    static getBuildableFrameworks(): string[];
}
//# sourceMappingURL=registry.d.ts.map