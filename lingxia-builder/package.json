{"name": "lingxia-builder", "version": "0.2.0", "description": "Modern build tool for LingXia MiniApp development. Supports HTML, Vue, and React with automatic function bridging and production optimization.", "type": "module", "main": "dist/index.js", "bin": {"lingxia": "./bin/lingxia.js"}, "exports": {".": "./dist/index.js"}, "files": ["bin/", "dist/", "src/", "README.md", "LICENSE"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "echo \"Tests coming soon\" && exit 0"}, "keywords": ["lingxia", "miniapp", "cli", "build-tool", "vue", "react", "html", "typescript", "vite", "mobile-app"], "author": "LingXia Team", "license": "MIT", "dependencies": {"commander": "^11.0.0"}, "devDependencies": {"@types/node": "^20.19.7", "typescript": "^5.0.0"}, "repository": {"type": "git", "url": "https://github.com/lingxia/lingxia.git"}, "bugs": {"url": "https://github.com/lingxia/lingxia/issues"}, "homepage": "https://github.com/lingxia/lingxia#readme"}