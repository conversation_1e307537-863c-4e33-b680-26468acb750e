import Foundation

#if os(iOS)
import UIKit
#elseif os(macOS)
import Cocoa
#endif

/// Shared navigation bar configuration
public struct NavigationBarConfig {
    let hidden: Bool
    let navigationBarBackgroundColor: PlatformColor?
    let navigationBarTextStyle: String?
    let navigationBarTitleText: String?
    let navigationStyle: String?

    static let DEFAULT_BACKGROUND_COLOR = PlatformColor.platformBackground
    static let DEFAULT_TEXT_COLOR = PlatformColor.platformLabel

    public init(
        hidden: Bool = false,
        navigationBarBackgroundColor: PlatformColor? = nil,
        navigationBarTextStyle: String? = nil,
        navigationBarTitleText: String? = nil,
        navigationStyle: String? = nil
    ) {
        self.hidden = hidden
        self.navigationBarBackgroundColor = navigationBarBackgroundColor
        self.navigationBarTextStyle = navigationBarTextStyle
        self.navigationBarTitleText = navigationBarTitleText
        self.navigationStyle = navigationStyle
    }

    public static func fromJson(_ json: String?) -> NavigationBarConfig? {
        guard let json = json, !json.isEmpty else {
            return NavigationBarConfig(hidden: true)
        }

        do {
            guard let data = json.data(using: .utf8),
                  let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
                return NavigationBarConfig(hidden: true)
            }

            let navStyle = jsonObject["navigationStyle"] as? String ?? "default"
            let isHidden = (jsonObject["hidden"] as? Bool ?? false) || navStyle == "custom"
            let textStyle = jsonObject["navigationBarTextStyle"] as? String ?? "black"

            return NavigationBarConfig(
                hidden: isHidden,
                navigationBarBackgroundColor: parseColor(jsonObject["navigationBarBackgroundColor"] as? String, defaultColor: DEFAULT_BACKGROUND_COLOR),
                navigationBarTextStyle: textStyle,
                navigationBarTitleText: jsonObject["navigationBarTitleText"] as? String ?? "",
                navigationStyle: navStyle
            )
        } catch {
            return NavigationBarConfig(hidden: true)
        }
    }

    private static func parseColor(_ colorString: String?, defaultColor: PlatformColor) -> PlatformColor {
        guard let colorString = colorString, !colorString.isEmpty else {
            return defaultColor
        }
        return PlatformColor(hexString: colorString) ?? defaultColor
    }
}

/// Protocol for navigation bar implementations
@MainActor
public protocol NavigationBarProtocol: AnyObject {
    var onBackClickListener: (() -> Void)? { get set }
    var onAnimationEndListener: (() -> Void)? { get set }

    func setTitle(_ title: String)
    func setBackButtonVisible(_ visible: Bool, animated: Bool)
    func updateConfig(_ config: NavigationBarConfig)
}

/// Shared navigation bar constants
public struct NavigationBarConstants {
    public static let BACK_BUTTON_WIDTH: CGFloat = 44
    public static let BACK_BUTTON_ICON_SIZE: CGFloat = 20
    public static let TITLE_FONT_SIZE: CGFloat = 17
    public static let TITLE_FONT_WEIGHT = PlatformFont.Weight.medium
    public static let TITLE_COLOR = PlatformColor.platformLabel
    public static let BACKGROUND_COLOR = PlatformColor(hexString: "#EDEDED") ?? PlatformColor.platformBackground
    public static let BORDER_COLOR = PlatformColor(hexString: "#DDDDDD") ?? PlatformColor.platformSecondaryLabel
}
