import Foundation
import CLingXiaFFI

#if os(iOS)
import UIKit

/// Main LxApp interface for iOS
@MainActor
public class LxApp {
    
    /// Initialize the LxApp system
    public static func initialize() {
        SharedLxApp.initialize()
    }
    
    /// Set home LxApp configuration
    public static func setHomeLxApp(appId: String, initialRoute: String = "/") {
        SharedLxApp.setHomeLxApp(appId: appId, initialRoute: initialRoute)
    }
    
    /// Set launch mode
    public static func setLaunchMode(_ mode: LxAppLaunchMode) {
        SharedLxApp.setLaunchMode(mode)
    }
    
    /// Open home LxApp
    public static func openHomeLxApp() {
        iOSLxApp.openHomeLxApp()
    }
    
    /// Open specific LxApp
    public static func openLxApp(appId: String, path: String = "/") {
        iOSLxApp.openLxApp(appId: appId, path: path)
    }
    
    /// Close LxApp
    public static func closeLxApp(appId: String) {
        iOSLxApp.closeLxApp(appId: appId)
    }
    
    /// Switch to page in LxApp
    public static func switchPage(appId: String, path: String) {
        iOSLxApp.switchPage(appId: appId, path: path)
    }
    
    /// Configure transparent system bars
    public static func configureTransparentSystemBars(viewController: UIViewController, lightStatusBarIcons: Bool = false) {
        iOSLxApp.configureTransparentSystemBars(viewController: viewController, lightStatusBarIcons: lightStatusBarIcons)
    }
}

#elseif os(macOS)
import Cocoa

/// Main LxApp interface for macOS
@MainActor
public class LxApp {
    
    /// Initialize the LxApp system
    public static func initialize() {
        SharedLxApp.initialize()
    }
    
    /// Set home LxApp configuration
    public static func setHomeLxApp(appId: String, initialRoute: String = "/") {
        SharedLxApp.setHomeLxApp(appId: appId, initialRoute: initialRoute)
    }
    
    /// Set window size for macOS
    public static func setWindowSize(width: CGFloat, height: CGFloat) {
        SharedLxApp.setWindowSize(width: width, height: height)
    }
    
    /// Open home LxApp
    public static func openHomeLxApp() {
        macOSLxApp.openHomeLxApp()
    }
    
    /// Open specific LxApp (FFI compatible)
    nonisolated public static func openLxApp(appid: RustStr, path: RustStr) -> Bool {
        return macOSLxApp.openLxApp(appid: appid, path: path)
    }
    
    /// Open specific LxApp (String version for convenience)
    public static func openLxApp(appId: String, path: String = "/") {
        macOSLxApp.openLxAppInternal(appId: appId, path: path)
    }
    
    /// Close LxApp (FFI compatible)
    nonisolated public static func closeLxApp(appid: RustStr) -> Bool {
        return macOSLxApp.closeLxApp(appid: appid)
    }
    
    /// Close LxApp (String version for convenience)
    public static func closeLxApp(appId: String) {
        macOSLxApp.closeLxAppInternal(appId: appId)
    }
    
    /// Switch to page in LxApp (FFI compatible)
    nonisolated public static func switchPage(appid: RustStr, path: RustStr) -> Bool {
        return macOSLxApp.switchPage(appid: appid, path: path)
    }
    
    /// Switch to page in LxApp (String version for convenience)
    public static func switchPage(appId: String, path: String) {
        macOSLxApp.switchPageInternal(appId: appId, path: path)
    }
}

#endif
