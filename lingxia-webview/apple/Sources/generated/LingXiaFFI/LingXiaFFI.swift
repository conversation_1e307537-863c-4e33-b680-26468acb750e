import Foundation
import CLingXiaFFI

public func miniappInit<GenericToRustStr: ToRustStr>(_ data_dir: GenericToRustStr, _ cache_dir: GenericToRustStr) -> Optional<RustString> {
    return cache_dir.toRustStr({ cache_dirAsRustStr in
        return data_dir.toRustStr({ data_dirAsRustStr in
        { let val = __swift_bridge__$miniapp_init(data_dirAsRustStr, cache_dirAsRustStr); if val != nil { return RustString(ptr: val!) } else { return nil } }()
    })
    })
}
public func onPageShow<GenericToRustStr: ToRustStr>(_ appid: GenericToRustStr, _ path: GenericToRustStr) {
    path.toRustStr({ pathAsRustStr in
        appid.toRustStr({ appidAsRustStr in
        __swift_bridge__$on_page_show(appidAsRustStr, pathAsRustStr)
    })
    })
}
public func onMiniappClosed<GenericToRustStr: ToRustStr>(_ appid: GenericToRustStr) -> Int32 {
    return appid.toRustStr({ appidAsRustStr in
        __swift_bridge__$on_miniapp_closed(appidAsRustStr)
    })
}
public func getPageConfig<GenericToRustStr: ToRustStr>(_ appid: GenericToRustStr, _ path: GenericToRustStr) -> Optional<RustString> {
    return path.toRustStr({ pathAsRustStr in
        return appid.toRustStr({ appidAsRustStr in
        { let val = __swift_bridge__$get_page_config(appidAsRustStr, pathAsRustStr); if val != nil { return RustString(ptr: val!) } else { return nil } }()
    })
    })
}
public func onBackPressed<GenericToRustStr: ToRustStr>(_ appid: GenericToRustStr) -> Bool {
    return appid.toRustStr({ appidAsRustStr in
        __swift_bridge__$on_back_pressed(appidAsRustStr)
    })
}
public func onMiniappOpened<GenericToRustStr: ToRustStr>(_ appid: GenericToRustStr, _ path: GenericToRustStr) -> Int32 {
    return path.toRustStr({ pathAsRustStr in
        return appid.toRustStr({ appidAsRustStr in
        __swift_bridge__$on_miniapp_opened(appidAsRustStr, pathAsRustStr)
    })
    })
}
public func getTabBarConfig<GenericToRustStr: ToRustStr>(_ appid: GenericToRustStr) -> Optional<RustString> {
    return appid.toRustStr({ appidAsRustStr in
        { let val = __swift_bridge__$get_tab_bar_config(appidAsRustStr); if val != nil { return RustString(ptr: val!) } else { return nil } }()
    })
}
public func findWebView<GenericToRustStr: ToRustStr>(_ appid: GenericToRustStr, _ path: GenericToRustStr) -> UInt {
    return path.toRustStr({ pathAsRustStr in
        return appid.toRustStr({ appidAsRustStr in
        __swift_bridge__$find_webview(appidAsRustStr, pathAsRustStr)
    })
    })
}
@_cdecl("__swift_bridge__$open_lxapp")
func __swift_bridge__open_lxapp (_ appid: RustStr, _ path: RustStr) -> Bool {
    LxApp.openLxApp(appid: appid, path: path)
}

@_cdecl("__swift_bridge__$close_miniapp")
func __swift_bridge__close_miniapp (_ appid: RustStr) -> Bool {
    LxApp.closeLxApp(appid: appid)
}

@_cdecl("__swift_bridge__$switch_page")
func __swift_bridge__switch_page (_ appid: RustStr, _ path: RustStr) -> Bool {
    LxApp.switchPage(appid: appid, path: path)
}



