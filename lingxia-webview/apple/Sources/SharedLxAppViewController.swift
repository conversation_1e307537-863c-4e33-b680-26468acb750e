import Foundation
import WebKit
import os.log

#if os(iOS)
import UIKit
#elseif os(macOS)
import Cocoa
#endif

/// Shared base class for LxApp view controllers
@MainActor
public class SharedLxAppViewController: PlatformViewController {
    nonisolated(unsafe) private static let log = OSLog(subsystem: "LingXia", category: "SharedLxAppViewController")
    
    // MARK: - Properties
    internal var appId: String
    private var initialPath: String
    private var isDisplayingHomeLxApp: Bool = false
    
    // UI Components
    internal var rootContainer: PlatformView!
    internal var statusBarBackground: PlatformView!
    internal var webViewContainer: PlatformView!
    internal var currentWebView: WKWebView?
    
    // Navigation and Tab Bar (will be implemented by platform-specific subclasses)
    internal var navigationBar: NavigationBarProtocol?
    internal var tabBar: TabBarProtocol?
    
    // Observers
    nonisolated(unsafe) private var closeAppObserver: NSObjectProtocol?
    nonisolated(unsafe) private var switchPageObserver: NSObjectProtocol?
    
    // MARK: - Initialization
    public init(appId: String, path: String) {
        self.appId = appId
        self.initialPath = path
        self.isDisplayingHomeLxApp = SharedLxApp.isHomeLxApp(appId)
        
        #if os(iOS)
        super.init(nibName: nil, bundle: nil)
        #else
        super.init(nibName: nil, bundle: nil)
        #endif
        
        setupNotificationObservers()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
        os_log("Deinitializing SharedLxAppViewController for appId: %@", log: Self.log, type: .info, appId)
        // Remove observers synchronously in deinit
        if let observer = closeAppObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        if let observer = switchPageObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }
    
    // MARK: - Lifecycle
    #if os(iOS)
    public override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadInitialPage()
    }
    #else
    public override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadInitialPage()
    }
    #endif
    
    // MARK: - UI Setup (to be implemented by platform-specific subclasses)
    internal func setupUI() {
        fatalError("setupUI() must be implemented by platform-specific subclasses")
    }
    
    internal func createNavigationBar() -> NavigationBarProtocol? {
        fatalError("createNavigationBar() must be implemented by platform-specific subclasses")
    }
    
    internal func createTabBar() -> TabBarProtocol? {
        fatalError("createTabBar() must be implemented by platform-specific subclasses")
    }
    
    // MARK: - Shared Logic
    private func setupNotificationObservers() {
        closeAppObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name(ACTION_CLOSE_LXAPP),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            let appId = notification.userInfo?["appId"] as? String
            Task { @MainActor in
                guard let self = self,
                      let targetAppId = appId,
                      targetAppId == self.appId else {
                    return
                }
                self.closeApp()
            }
        }
        
        switchPageObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name(ACTION_SWITCH_PAGE),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            let appId = notification.userInfo?["appId"] as? String
            let path = notification.userInfo?["path"] as? String
            Task { @MainActor in
                guard let self = self,
                      let targetAppId = appId,
                      targetAppId == self.appId,
                      let targetPath = path else {
                    return
                }
                self.switchToPage(targetPath)
            }
        }
    }
    
    private func removeNotificationObservers() {
        if let observer = closeAppObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        if let observer = switchPageObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }
    

    
    internal func loadInitialPage() {
        let path = SharedLxApp.getLastActivePath(for: appId)
        let targetPath = path.isEmpty ? initialPath : path

        // FIRST: Call onMiniappOpened to let Rust create the WebView
        os_log("🔧 Calling onMiniappOpened for appId: %@ path: %@", log: Self.log, type: .info, appId, targetPath)
        let openResult = onMiniappOpened(appId, targetPath)
        os_log("✅ onMiniappOpened completed with result: %d", log: Self.log, type: .info, openResult)

        // THEN: Switch to the page (which will find the WebView created by Rust)
        switchToPage(targetPath)
    }
    
    internal func switchToPage(_ path: String) {
        os_log("Switching to page: %@ for appId: %@", log: Self.log, type: .info, path, appId)
        
        // Find or create WebView
        if let webView = SharedWebViewManager.findWebView(appId: appId, path: path) {
            attachWebView(webView, path: path)
        } else {
            os_log("No WebView found for %@ at %@", log: Self.log, type: .error, appId, path)
        }
        
        SharedLxApp.setLastActivePath(path, for: appId)
    }
    
    private func attachWebView(_ webView: WKWebView, path: String) {
        // Remove current WebView
        currentWebView?.removeFromSuperview()

        // Add new WebView
        currentWebView = webView
        webViewContainer.addSubview(webView)

        // Setup constraints (platform-specific implementation needed)
        setupWebViewConstraints(webView)

        // Mark WebView as registered (onMiniappOpened was already called in loadInitialPage)
        webView.isRegistered = true

        os_log("WebView attached for %@ at %@", log: Self.log, type: .info, appId, path)
    }
    
    // MARK: - Platform-specific methods (to be implemented by subclasses)
    internal func setupWebViewConstraints(_ webView: WKWebView) {
        fatalError("setupWebViewConstraints(_:) must be implemented by platform-specific subclasses")
    }
    
    internal func closeApp() {
        fatalError("closeApp() must be implemented by platform-specific subclasses")
    }
}
