#if os(macOS)
import Foundation
import WebKit
import os.log
import Cocoa
import CLingXiaFFI

private let lxAppViewControllerLog = OSLog(subsystem: "LingXia", category: "LxAppView")

@MainActor
public class macOSLxAppViewController: NSViewController, WKNavigationDelegate {
    nonisolated(unsafe) private static let log = lxAppViewControllerLog

    // MARK: - Constants
    private static let TAB_BAR_HEIGHT: CGFloat = 40
    internal static let DEFAULT_NAV_BAR_HEIGHT: CGFloat = 32 // This constant is no longer used for layout, but kept for reference if needed elsewhere

    // MARK: - Properties
    internal var appId: String
    private var initialPath: String
    private var webViewContainer: NSView!
    private var tabBarView: NSView?
    private var currentWebView: WKWebView?
    // Removed: private var navigationBar: LingXiaNavigationBar!

    nonisolated(unsafe) private var closeAppObserver: NSObjectProtocol?
    nonisolated(unsafe) private var switchPageObserver: NSObjectProtocol?

    public init(appId: String, path: String) {
        self.appId = appId
        self.initialPath = path
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    deinit {
        os_log("Deinitializing LxAppViewController for appId: %@", log: Self.log, type: .info, appId)
        if let observer = closeAppObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        if let observer = switchPageObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }

    // MARK: - Lifecycle
    public override func loadView() {
        view = NSView()
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.windowBackgroundColor.cgColor
    }

    public override func viewDidLoad() {
        super.viewDidLoad()
        os_log("ViewDidLoad started for appId: %@", log: Self.log, type: .info, appId)
        
        setupLayout()
        setupNotificationObservers()
        loadWebViewContent()
        
        os_log("ViewDidLoad completed", log: Self.log, type: .info)
    }

    // MARK: - UI Setup
    private func setupLayout() {
        // Create and add views
        // Removed: setupNavigationBar()
        setupTabBar() // This just creates the view, doesn't add it as a subview yet
        setupWebViewContainer()

        // --- Activate Constraints ---
        var constraints: [NSLayoutConstraint] = []

        // WebViewContainer constraints
        // Top constraint depends on TabBar position
        constraints.append(contentsOf: [
            webViewContainer.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            webViewContainer.trailingAnchor.constraint(equalTo: view.trailingAnchor)
        ])

        // TabBar and final WebViewContainer constraints
        if let tabBar = tabBarView {
            view.addSubview(tabBar)
            if let tabBarConfig = getTabBarConfig(appId), let config = TabBarConfig.fromJson(tabBarConfig.toString()), config.position == "top" {
                // TabBar at top
                constraints.append(contentsOf: [
                    tabBar.topAnchor.constraint(equalTo: view.topAnchor),
                    webViewContainer.topAnchor.constraint(equalTo: tabBar.bottomAnchor)
                ])
            } else {
                // TabBar at bottom (default)
                constraints.append(contentsOf: [
                    tabBar.bottomAnchor.constraint(equalTo: view.bottomAnchor),
                    webViewContainer.bottomAnchor.constraint(equalTo: tabBar.topAnchor)
                ])
            }

            constraints.append(contentsOf: [
                tabBar.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                tabBar.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                tabBar.heightAnchor.constraint(equalToConstant: Self.TAB_BAR_HEIGHT)
            ])
        } else {
            // No TabBar, WebView fills the entire view
            constraints.append(contentsOf: [
                webViewContainer.topAnchor.constraint(equalTo: view.topAnchor),
                webViewContainer.bottomAnchor.constraint(equalTo: view.bottomAnchor)
            ])
        }
        
        NSLayoutConstraint.activate(constraints)
    }

    // Removed: private func setupNavigationBar() { ... }

    private func setupWebViewContainer() {
        webViewContainer = NSView()
        webViewContainer.wantsLayer = true
        webViewContainer.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(webViewContainer)
    }

    private func setupTabBar() {
        guard let tabBarConfigRust = getTabBarConfig(appId) else {
            os_log("No TabBar config found for appId: %@", log: Self.log, type: .info, appId)
            return
        }

        let tabBarConfigJson = tabBarConfigRust.toString()
        guard let tabBarConfig = TabBarConfig.fromJson(tabBarConfigJson) else {
            os_log("Failed to parse TabBar config for appId: %@", log: Self.log, type: .error, appId)
            return
        }

        let tabBar = NSView()
        tabBar.wantsLayer = true
        tabBar.layer?.backgroundColor = (tabBarConfig.backgroundColor ?? TabBarConfig.DEFAULT_BACKGROUND_COLOR).cgColor
        tabBar.translatesAutoresizingMaskIntoConstraints = false

        let stackView = NSStackView()
        stackView.orientation = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .centerY
        stackView.spacing = 0
        stackView.translatesAutoresizingMaskIntoConstraints = false
        tabBar.addSubview(stackView)

        NSLayoutConstraint.activate([
            stackView.leadingAnchor.constraint(equalTo: tabBar.leadingAnchor),
            stackView.trailingAnchor.constraint(equalTo: tabBar.trailingAnchor),
            stackView.topAnchor.constraint(equalTo: tabBar.topAnchor),
            stackView.bottomAnchor.constraint(equalTo: tabBar.bottomAnchor)
        ])

        for (index, item) in tabBarConfig.items.enumerated() {
            let button = NSButton(title: item.text, target: self, action: #selector(tabButtonClicked(_:)))
            button.tag = index
            button.bezelStyle = .regularSquare
            button.isBordered = false
            button.wantsLayer = true
            button.layer?.backgroundColor = NSColor.clear.cgColor
            
            let attributedTitle = NSAttributedString(
                string: item.text,
                attributes: [
                    .foregroundColor: NSColor.labelColor,
                    .font: NSFont.systemFont(ofSize: 12, weight: .medium)
                ]
            )
            button.attributedTitle = attributedTitle

            if let iconPath = item.iconPath {
                setButtonIcon(button: button, iconPath: iconPath)
            }

            button.imagePosition = .imageAbove
            button.imageScaling = .scaleProportionallyDown
            stackView.addArrangedSubview(button)
        }

        self.tabBarView = tabBar
        os_log("TabBar setup completed with %d items", log: Self.log, type: .info, tabBarConfig.items.count)
    }

    private func loadWebViewContent() {
        os_log("Loading WebView content...", log: Self.log, type: .info)
        
        let _ = findWebView(appId, initialPath)
        
        if let webView = SharedWebViewManager.findWebView(appId: appId, path: initialPath) {
            attachWebViewToContainer(webView)
            os_log("WebView found and attached immediately", log: Self.log, type: .info)
        } else {
            os_log("WebView not found, will retry...", log: Self.log, type: .info)
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                if let webView = SharedWebViewManager.findWebView(appId: self.appId, path: self.initialPath) {
                    self.attachWebViewToContainer(webView)
                    os_log("WebView found and attached after retry", log: Self.log, type: .info)
                } else {
                    os_log("WebView still not found after retry", log: Self.log, type: .error)
                }
            }
        }
    }
    
    private func attachWebViewToContainer(_ webView: WKWebView) {
        currentWebView?.removeFromSuperview()
        currentWebView = webView
        
        webView.translatesAutoresizingMaskIntoConstraints = false
        webViewContainer.addSubview(webView)
        
        NSLayoutConstraint.activate([
            webView.topAnchor.constraint(equalTo: webViewContainer.topAnchor),
            webView.leadingAnchor.constraint(equalTo: webViewContainer.leadingAnchor),
            webView.trailingAnchor.constraint(equalTo: webViewContainer.trailingAnchor),
            webView.bottomAnchor.constraint(equalTo: webViewContainer.bottomAnchor)
        ])
        
        webView.navigationDelegate = self
        let _ = onPageShow(appId, initialPath)
        os_log("WebView attached to container", log: Self.log, type: .info)
    }

    private func setupNotificationObservers() {
        closeAppObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name(ACTION_CLOSE_LXAPP), object: nil, queue: .main
        ) { [weak self] notification in
            let appId = notification.userInfo?["appId"] as? String
            Task { @MainActor in
                guard let self = self, let targetAppId = appId, targetAppId == self.appId else { return }
                os_log("Received close request for appId: %@", log: Self.log, type: .info, self.appId)
                self.view.window?.close()
            }
        }

        switchPageObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name(ACTION_SWITCH_PAGE), object: nil, queue: .main
        ) { [weak self] notification in
            let appId = notification.userInfo?["appId"] as? String
            let path = notification.userInfo?["path"] as? String
            Task { @MainActor in
                guard let self = self, let targetAppId = appId, let targetPath = path, targetAppId == self.appId else { return }
                os_log("Received switch page notification - appId: %@ path: %@", log: Self.log, type: .info, self.appId, targetPath)
                self.switchPage(targetPath: targetPath)
            }
        }
        os_log("Notification observers set up for appId: %@", log: Self.log, type: .info, appId)
    }

    // MARK: - Page Switching
    public func switchPage(targetPath: String) {
        self.initialPath = targetPath
        os_log("Switching to page: %@", log: Self.log, type: .info, targetPath)

        let _ = onPageShow(appId, targetPath)
        let _ = findWebView(appId, targetPath)
        
        if let webView = SharedWebViewManager.findWebView(appId: appId, path: targetPath) {
            attachWebViewToContainer(webView)
            os_log("✅ Switched to page: %@", log: Self.log, type: .info, targetPath)
        } else {
            os_log("❌ WebView not found for page: %@", log: Self.log, type: .error, targetPath)
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                if let webView = SharedWebViewManager.findWebView(appId: self.appId, path: targetPath) {
                    self.attachWebViewToContainer(webView)
                    os_log("✅ WebView found after retry and attached", log: Self.log, type: .info)
                }
            }
        }
        SharedLxApp.setLastActivePath(targetPath, for: appId)
    }

    // MARK: - TabBar Actions
    @objc private func tabButtonClicked(_ sender: NSButton) {
        let index = sender.tag
        os_log("Tab button clicked: index=%d", log: Self.log, type: .info, index)

        guard let tabBarConfigRust = getTabBarConfig(appId),
              let tabBarConfig = TabBarConfig.fromJson(tabBarConfigRust.toString()),
              index < tabBarConfig.items.count else {
            return
        }
        let item = tabBarConfig.items[index]
        switchPage(targetPath: item.pagePath)
    }

    // MARK: - Helpers
    private func setButtonIcon(button: NSButton, iconPath: String) {
        var image: NSImage?
        if iconPath.hasPrefix("SF:") {
            let symbolName = String(iconPath.dropFirst(3))
            if #available(macOS 11.0, *) {
                image = NSImage(systemSymbolName: symbolName, accessibilityDescription: nil)
                image?.isTemplate = true
            }
        } else if iconPath.hasPrefix("/") {
            image = NSImage(contentsOfFile: iconPath)
        } else {
            image = NSImage(named: iconPath)
            if image == nil {
                let resourcesPath = getResourcesPath()
                let fullPath = "\(resourcesPath)/homelxapp/\(iconPath)"
                image = NSImage(contentsOfFile: fullPath)
            }
        }

        if let img = image {
            let iconSize = NSSize(width: 22, height: 22)
            button.image = resizeImage(img, to: iconSize)
        }
    }

    private func getResourcesPath() -> String {
        let executablePath = Bundle.main.executablePath ?? ""
        let executableDir = (executablePath as NSString).deletingLastPathComponent
        return "\(executableDir)/Resources"
    }

    private func resizeImage(_ image: NSImage, to size: NSSize) -> NSImage {
        let resizedImage = NSImage(size: size)
        resizedImage.lockFocus()
        image.draw(in: NSRect(origin: .zero, size: size))
        resizedImage.unlockFocus()
        resizedImage.isTemplate = image.isTemplate
        return resizedImage
    }

    // MARK: - WKNavigationDelegate
    public func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        os_log("✅ WebView finished loading successfully", log: Self.log, type: .info)
    }

    public func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        os_log("❌ WebView failed to load: %@", log: Self.log, type: .error, error.localizedDescription)
    }

    public func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        os_log("❌ WebView provisional navigation failed: %@", log: Self.log, type: .error, error.localizedDescription)
    }
}

// MARK: - LingXiaNavigationBar Class (No longer used for main navigation bar in macOS)
public class LingXiaNavigationBar: NSView {
    private static let TITLE_FONT_SIZE: CGFloat = 17
    private static let TITLE_FONT_WEIGHT: NSFont.Weight = .medium
    private static let BACKGROUND_COLOR = NSColor(red: 0.93, green: 0.93, blue: 0.93, alpha: 1.0)
    private static let BORDER_COLOR = NSColor(red: 0.87, green: 0.87, blue: 0.87, alpha: 1.0)

    private var titleLabel: NSTextField!

    public override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }

    private func setupView() {
        wantsLayer = true
        layer?.backgroundColor = Self.BACKGROUND_COLOR.cgColor

        let bottomBorder = NSView()
        bottomBorder.wantsLayer = true
        bottomBorder.layer?.backgroundColor = Self.BORDER_COLOR.cgColor
        bottomBorder.translatesAutoresizingMaskIntoConstraints = false
        addSubview(bottomBorder)

        titleLabel = NSTextField(labelWithString: "")
        titleLabel.font = NSFont.systemFont(ofSize: Self.TITLE_FONT_SIZE, weight: Self.TITLE_FONT_WEIGHT)
        titleLabel.textColor = NSColor.labelColor
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        addSubview(titleLabel)

        NSLayoutConstraint.activate([
            bottomBorder.leadingAnchor.constraint(equalTo: leadingAnchor),
            bottomBorder.trailingAnchor.constraint(equalTo: trailingAnchor),
            bottomBorder.bottomAnchor.constraint(equalTo: bottomAnchor),
            bottomBorder.heightAnchor.constraint(equalToConstant: 0.5),
            
            titleLabel.centerXAnchor.constraint(equalTo: centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: centerYAnchor)
        ])
    }

    public func setTitle(_ title: String) {
        titleLabel.stringValue = title
    }
}
#endif