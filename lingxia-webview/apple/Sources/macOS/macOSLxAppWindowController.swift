import lingxia
import Cocoa
import WebKit
import os.log

// Device size configurations for different Apple devices
public enum DeviceSize: String, CaseIterable {
    case iphone = "iphone"
    case ipad = "ipad"
    case ipadLandscape = "ipad-landscape"
    case ipadAir = "ipadair"
    case ipadAirLandscape = "ipadair-landscape"
    case ipadPro11 = "ipadpro11"
    case ipadPro11Landscape = "ipadpro11-landscape"
    case ipadPro129 = "ipadpro129"
    case ipadPro129Landscape = "ipadpro129-landscape"
    case custom = "custom"
    
    public var size: (width: CGFloat, height: CGFloat) {
        switch self {
        case .iphone:
            return (414, 896) // iPhone 11 Pro Max size
        case .ipad:
            return (810, 1080) // iPad 10.2" portrait
        case .ipadLandscape:
            return (1080, 810) // iPad 10.2" landscape
        case .ipadAir:
            return (820, 1180) // iPad Air 10.9" portrait
        case .ipadAirLandscape:
            return (1180, 820) // iPad Air 10.9" landscape
        case .ipadPro11:
            return (834, 1194) // iPad Pro 11" portrait
        case .ipadPro11Landscape:
            return (1194, 834) // iPad Pro 11" landscape
        case .ipadPro129:
            return (1024, 1366) // iPad Pro 12.9" portrait
        case .ipadPro129Landscape:
            return (1366, 1024) // iPad Pro 12.9" landscape
        case .custom:
            return (414, 896) // Default to iPhone if custom
        }
    }
    
    public var description: String {
        switch self {
        case .iphone:
            return "iPhone (414x896)"
        case .ipad:
            return "iPad 10.2\" Portrait (810x1080)"
        case .ipadLandscape:
            return "iPad 10.2\" Landscape (1080x810)"
        case .ipadAir:
            return "iPad Air 10.9\" Portrait (820x1180)"
        case .ipadAirLandscape:
            return "iPad Air 10.9\" Landscape (1180x820)"
        case .ipadPro11:
            return "iPad Pro 11\" Portrait (834x1194)"
        case .ipadPro11Landscape:
            return "iPad Pro 11\" Landscape (1194x834)"
        case .ipadPro129:
            return "iPad Pro 12.9\" Portrait (1024x1366)"
        case .ipadPro129Landscape:
            return "iPad Pro 12.9\" Landscape (1366x1024)"
        case .custom:
            return "Custom size"
        }
    }
}

// Global configuration struct (moved here for compilation)
public struct AppConfig {
    @MainActor public static var selectedDeviceSize: DeviceSize = .iphone
}

private let lxAppWindowControllerLog = OSLog(subsystem: "LingXia", category: "LxAppWindow")

@MainActor
public class macOSLxAppWindowController: NSWindowController {
    private static let log = lxAppWindowControllerLog
    
    internal var appId: String
    private var initialPath: String
    private var lxAppViewController: macOSLxAppViewController!
    private var customTitleBarView: NSView!
    private let customTitleBarHeight: CGFloat = 32

    init(appId: String, path: String) {
        self.appId = appId
        self.initialPath = path
        
        let (width, height) = AppConfig.selectedDeviceSize.size
        let contentRect = NSRect(x: 0, y: 0, width: width, height: height)
        
        var styleMask: NSWindow.StyleMask = [.titled, .closable, .miniaturizable]
        if AppConfig.selectedDeviceSize == .custom {
            styleMask.insert(.resizable)
        }

        let window = NSWindow(
            contentRect: contentRect,
            styleMask: styleMask,
            backing: .buffered,
            defer: false
        )
        
        super.init(window: window)

        setupWindow()
        setupCustomTitleBar()
        setupViewController()

        window.center()
        window.makeKeyAndOrderFront(nil as Any?)
        NSApp.activate(ignoringOtherApps: true)

        os_log("Window created for appId=%@", log: Self.log, type: .info, appId)
        print("DEBUG: Window created for appId=\(appId)")
        print("DEBUG: Window frame: \(NSStringFromRect(window.frame))")
        print("DEBUG: Window isVisible: \(window.isVisible)")
        print("DEBUG: Window isKeyWindow: \(window.isKeyWindow)")
        print("DEBUG: Window level: \(window.level.rawValue)")
        print("DEBUG: Window alphaValue: \(window.alphaValue)")
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupWindow() {
        guard let window = window else { return }
        
        window.title = "LingXia - \(appId)"
        window.isReleasedWhenClosed = false
        window.delegate = self
        
        // Configure window for custom controls
        window.titlebarAppearsTransparent = true
        window.titleVisibility = .hidden
        window.styleMask.insert(.fullSizeContentView)
        window.isMovableByWindowBackground = true
        
        // Hide standard traffic light buttons
        window.standardWindowButton(.closeButton)?.isHidden = true
        window.standardWindowButton(.miniaturizeButton)?.isHidden = true
        window.standardWindowButton(.zoomButton)?.isHidden = true
    }
    
    private func setupCustomTitleBar() {
        guard let contentView = window?.contentView else { return }

        customTitleBarView = NSView()
        customTitleBarView.wantsLayer = true
        customTitleBarView.layer?.backgroundColor = NSColor.white.cgColor
        customTitleBarView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(customTitleBarView)

        // Add shadow line at bottom
        let shadowView = NSView()
        shadowView.wantsLayer = true
        shadowView.layer?.backgroundColor = NSColor.black.withAlphaComponent(0.1).cgColor
        shadowView.translatesAutoresizingMaskIntoConstraints = false
        customTitleBarView.addSubview(shadowView)

        // Add title label
        let titleLabel = NSTextField(labelWithString: getWindowTitle())
        titleLabel.font = NSFont.systemFont(ofSize: 17, weight: .semibold)
        titleLabel.textColor = NSColor.black
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        customTitleBarView.addSubview(titleLabel)

        // Create capsule buttons
        let moreButton = createStandardButton(image: createThreeDotsImage(), target: self, action: #selector(moreButtonTapped))
        moreButton.toolTip = "More"

        let minimizeButton = createStandardButton(image: createMinimizeButtonImage(), target: self, action: #selector(minimizeWindow))
        minimizeButton.toolTip = "Minimize"

        let closeButton = createStandardButton(image: createCloseButtonImage(), target: self, action: #selector(closeWindow))
        closeButton.toolTip = "Close"

        let stackView = NSStackView(views: [moreButton, minimizeButton, closeButton])
        stackView.orientation = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 0
        stackView.translatesAutoresizingMaskIntoConstraints = false
        
        // Add separators between buttons
        let separator1 = NSView()
        separator1.wantsLayer = true
        separator1.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.4).cgColor
        separator1.translatesAutoresizingMaskIntoConstraints = false
        
        let separator2 = NSView()
        separator2.wantsLayer = true
        separator2.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.4).cgColor
        separator2.translatesAutoresizingMaskIntoConstraints = false

        let capsuleContainer = NSVisualEffectView()
        capsuleContainer.blendingMode = .withinWindow
        capsuleContainer.material = .titlebar
        capsuleContainer.state = .active
        capsuleContainer.wantsLayer = true
        capsuleContainer.layer?.cornerRadius = 14 // Half of 28 (capsule button height)
        capsuleContainer.translatesAutoresizingMaskIntoConstraints = false
        
        capsuleContainer.addSubview(stackView)
        capsuleContainer.addSubview(separator1)
        capsuleContainer.addSubview(separator2)
        customTitleBarView.addSubview(capsuleContainer)

        // Constraints for customTitleBarView
        NSLayoutConstraint.activate([
            customTitleBarView.topAnchor.constraint(equalTo: contentView.topAnchor),
            customTitleBarView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            customTitleBarView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            customTitleBarView.heightAnchor.constraint(equalToConstant: customTitleBarHeight)
        ])

        // Constraints for shadowView
        NSLayoutConstraint.activate([
            shadowView.leadingAnchor.constraint(equalTo: customTitleBarView.leadingAnchor),
            shadowView.trailingAnchor.constraint(equalTo: customTitleBarView.trailingAnchor),
            shadowView.bottomAnchor.constraint(equalTo: customTitleBarView.bottomAnchor),
            shadowView.heightAnchor.constraint(equalToConstant: 1)
        ])

        // Constraints for titleLabel
        NSLayoutConstraint.activate([
            titleLabel.centerXAnchor.constraint(equalTo: customTitleBarView.centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: customTitleBarView.centerYAnchor)
        ])

        // Constraints for capsuleContainer
        let capsuleWidth: CGFloat = 87
        let capsuleHeight: CGFloat = 28
        let capsuleTopMargin: CGFloat = 2
        let capsuleRightMargin: CGFloat = 7

        NSLayoutConstraint.activate([
            capsuleContainer.widthAnchor.constraint(equalToConstant: capsuleWidth),
            capsuleContainer.heightAnchor.constraint(equalToConstant: capsuleHeight),
            capsuleContainer.topAnchor.constraint(equalTo: customTitleBarView.topAnchor, constant: capsuleTopMargin),
            capsuleContainer.trailingAnchor.constraint(equalTo: customTitleBarView.trailingAnchor, constant: -capsuleRightMargin)
        ])
        
        // Constraints for stackView inside capsuleContainer
        NSLayoutConstraint.activate([
            stackView.leadingAnchor.constraint(equalTo: capsuleContainer.leadingAnchor),
            stackView.trailingAnchor.constraint(equalTo: capsuleContainer.trailingAnchor),
            stackView.topAnchor.constraint(equalTo: capsuleContainer.topAnchor),
            stackView.bottomAnchor.constraint(equalTo: capsuleContainer.bottomAnchor)
        ])
        
        // Constraints for separators
        NSLayoutConstraint.activate([
            separator1.centerYAnchor.constraint(equalTo: stackView.centerYAnchor),
            separator1.centerXAnchor.constraint(equalTo: moreButton.trailingAnchor),
            separator1.heightAnchor.constraint(equalToConstant: capsuleHeight - 8),
            separator1.widthAnchor.constraint(equalToConstant: 1),
            
            separator2.centerYAnchor.constraint(equalTo: stackView.centerYAnchor),
            separator2.centerXAnchor.constraint(equalTo: minimizeButton.trailingAnchor),
            separator2.heightAnchor.constraint(equalToConstant: capsuleHeight - 8),
            separator2.widthAnchor.constraint(equalToConstant: 1)
        ])
    }

    private func getWindowTitle() -> String {
        // TODO: Get actual app title from LxApp configuration
        return "LingXia"
    }

    private func setupViewController() {
        guard let window = window else {
            os_log("❌ No window available for setupViewController", log: Self.log, type: .error)
            return
        }

        lxAppViewController = macOSLxAppViewController(appId: appId, path: initialPath)
        window.contentViewController = lxAppViewController
        
        // Adjust the contentViewController's view to be below the custom title bar
        let view = lxAppViewController.view
        view.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            view.topAnchor.constraint(equalTo: customTitleBarView.bottomAnchor),
            view.leadingAnchor.constraint(equalTo: window.contentView!.leadingAnchor),
            view.trailingAnchor.constraint(equalTo: window.contentView!.trailingAnchor),
            view.bottomAnchor.constraint(equalTo: window.contentView!.bottomAnchor)
        ])
        
        os_log("✅ setupViewController completed", log: Self.log, type: .info)
    }

    // MARK: - Button Actions
    @objc private func moreButtonTapped() {
        os_log("More button tapped", log: Self.log, type: .info)
        // TODO: Implement more options menu
    }

    @objc private func minimizeWindow() {
        window?.miniaturize(nil)
    }

    @objc private func closeWindow() {
        window?.close()
    }

    // MARK: - Button Creation Helpers
    private func createStandardButton(image: NSImage?, target: AnyObject?, action: Selector?) -> NSButton {
        let button = NSButton(image: image ?? NSImage(), target: target, action: action)
        button.bezelStyle = .regularSquare
        button.isBordered = false
        button.imagePosition = .imageOnly
        button.imageScaling = .scaleProportionallyDown
        button.wantsLayer = true
        button.layer?.backgroundColor = NSColor.clear.cgColor
        button.setButtonType(.momentaryPushIn)
        button.allowedTouchTypes = .direct
        button.translatesAutoresizingMaskIntoConstraints = false // Use Auto Layout
        return button
    }

    private func createThreeDotsImage() -> NSImage? {
        let size = CGSize(width: 24, height: 24)
        let image = NSImage(size: size)
        image.lockFocus()

        if let context = NSGraphicsContext.current?.cgContext {
            context.setFillColor(NSColor.black.cgColor)

            let dotRadius: CGFloat = 1.5
            let dotSpacing: CGFloat = 4
            let totalWidth = 3 * (dotRadius * 2) + 2 * dotSpacing
            let startX = (size.width - totalWidth) / 2
            let centerY = size.height / 2

            for i in 0..<3 {
                let x = startX + CGFloat(i) * (dotRadius * 2 + dotSpacing) + dotRadius
                let dotRect = CGRect(x: x - dotRadius, y: centerY - dotRadius, width: dotRadius * 2, height: dotRadius * 2)
                context.fillEllipse(in: dotRect)
            }
        }

        image.unlockFocus()
        return image
    }

    private func createMinimizeButtonImage() -> NSImage? {
        let size = CGSize(width: 24, height: 24)
        let image = NSImage(size: size)
        image.lockFocus()

        if let context = NSGraphicsContext.current?.cgContext {
            context.setStrokeColor(NSColor.black.cgColor)
            context.setLineWidth(1.5)

            let lineY = size.height / 2
            let lineStartX: CGFloat = 8
            let lineEndX: CGFloat = 16

            context.move(to: CGPoint(x: lineStartX, y: lineY))
            context.addLine(to: CGPoint(x: lineEndX, y: lineY))
            context.strokePath()
        }

        image.unlockFocus()
        return image
    }

    private func createCloseButtonImage() -> NSImage? {
        let size = CGSize(width: 24, height: 24)
        let image = NSImage(size: size)
        image.lockFocus()

        if let context = NSGraphicsContext.current?.cgContext {
            context.setStrokeColor(NSColor.black.cgColor)
            context.setLineWidth(1.5)

            let margin: CGFloat = 8
            let startPoint1 = CGPoint(x: margin, y: margin)
            let endPoint1 = CGPoint(x: size.width - margin, y: size.height - margin)
            let startPoint2 = CGPoint(x: size.width - margin, y: margin)
            let endPoint2 = CGPoint(x: margin, y: size.height - margin)

            context.move(to: startPoint1)
            context.addLine(to: endPoint1)
            context.move(to: startPoint2)
            context.addLine(to: endPoint2)
            context.strokePath()
        }

        image.unlockFocus()
        return image
    }
}

extension macOSLxAppWindowController: NSWindowDelegate {
    public func windowWillClose(_ notification: Notification) {
        os_log("Window will close for appId=%@", log: Self.log, type: .info, appId)
        macOSLxApp.removeWindowController(self)
    }
    
    public func windowDidBecomeKey(_ notification: Notification) {
        os_log("Window became key for appId=%@", log: Self.log, type: .info, appId)
    }
}