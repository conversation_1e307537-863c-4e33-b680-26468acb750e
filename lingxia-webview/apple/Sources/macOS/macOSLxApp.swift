#if os(macOS)
import Foundation
import Cocoa
import os.log
import CLingXiaFFI

/// macOS-specific LxApp implementation
@MainActor
public class macOSLxApp {
    nonisolated(unsafe) private static let log = OSLog(subsystem: "LingXia", category: "macOSLxApp")
    
    private static var activeWindowControllers: [macOSLxAppWindowController] = []
    private static var hasInitialized = false
    
    /// Open home LxApp
    public static func openHomeLxApp() {
        guard let homeLxAppId = SharedLxApp.getHomeLxAppId() else {
            os_log("Home LxApp not configured", log: log, type: .error)
            return
        }

        let initialRoute = SharedLxApp.getHomeLxAppInitialRoute()
        openLxAppInternal(appId: homeLxAppId, path: initialRoute)
    }
    
    /// Open specific LxApp (FFI compatible version)
    nonisolated public static func openLxApp(appid: RustStr, path: RustStr) -> Bool {
        let appId = appid.toString()
        let pathString = path.toString()
        os_log("Opening LxApp: %@ at path: %@", log: log, type: .info, appId, pathString)
        
        // Check if we're already on main thread to avoid deadlock
        if Thread.isMainThread {
            MainActor.assumeIsolated {
                openLxAppInternal(appId: appId, path: pathString)
            }
        } else {
            DispatchQueue.main.sync {
                openLxAppInternal(appId: appId, path: pathString)
            }
        }
        return true
    }
    
    /// Internal implementation for opening LxApp
    internal static func openLxAppInternal(appId: String, path: String) {

        // Initialize LxApps if not already done
        macOSLxApp.initializeLxAppsIfNeeded()

        // Check if window already exists for this app
        if let existingController = activeWindowControllers.first(where: { $0.appId == appId }) {
            existingController.window?.makeKeyAndOrderFront(nil as Any?)
            switchPageInternal(appId: appId, path: path)
            return
        }

        // Get the last active path for state restoration (like iOS)
        let actualPath: String
        let storedPath = SharedLxApp.getLastActivePath(for: appId)

        if !storedPath.isEmpty && storedPath != path && appId != SharedLxApp.getHomeLxAppId() {
            actualPath = storedPath
            os_log("openLxAppInternal: Using stored path for state restoration: %@ (requested: %@)",
                   log: log, type: .info, actualPath, path)
        } else {
            actualPath = path
            os_log("openLxAppInternal: Using requested path: %@", log: log, type: .info, actualPath)
        }

        // Call onMiniappOpened FIRST to ensure WebView is created before window setup (like iOS)
        os_log("🔧 Calling onMiniappOpened for appId: %@ path: %@", log: log, type: .info, appId, actualPath)
        print("DEBUG: Calling onMiniappOpened for appId: \(appId) path: \(actualPath)")
        let openResult = onMiniappOpened(appId, actualPath)
        os_log("onMiniappOpened completed with result=%d for appId=%@ path=%@", log: log, type: .info, openResult, appId, actualPath)
        print("DEBUG: onMiniappOpened completed with result=\(openResult) for appId=\(appId) path=\(actualPath)")

        if openResult != 0 {
            print("DEBUG: ❌ onMiniappOpened failed with result=\(openResult)")
            return
        }

        // Create window controller with the actual path
        let windowController = macOSLxAppWindowController(appId: appId, path: actualPath)

        activeWindowControllers.append(windowController)

        // Show the window properly
        windowController.window?.makeKeyAndOrderFront(nil as Any?)
        NSApp.activate(ignoringOtherApps: true)

        os_log("Window controller created and window shown", log: log, type: .info)
        print("DEBUG: Window shown - isVisible: \(windowController.window?.isVisible ?? false)")
        print("DEBUG: Window shown - isKeyWindow: \(windowController.window?.isKeyWindow ?? false)")
    }
    
    /// Close LxApp (FFI compatible version)
    nonisolated public static func closeLxApp(appid: RustStr) -> Bool {
        let appId = appid.toString()
        if Thread.isMainThread {
            MainActor.assumeIsolated {
                closeLxAppInternal(appId: appId)
            }
        } else {
            DispatchQueue.main.sync {
                closeLxAppInternal(appId: appId)
            }
        }
        return true
    }
    
    /// Internal implementation for closing LxApp
    internal static func closeLxAppInternal(appId: String) {
        if let controller = activeWindowControllers.first(where: { $0.appId == appId }) {
            controller.window?.close()
        }
    }

    /// Switch to page in LxApp (FFI compatible version)
    nonisolated public static func switchPage(appid: RustStr, path: RustStr) -> Bool {
        let appId = appid.toString()
        let pathString = path.toString()
        if Thread.isMainThread {
            MainActor.assumeIsolated {
                switchPageInternal(appId: appId, path: pathString)
            }
        } else {
            DispatchQueue.main.sync {
                switchPageInternal(appId: appId, path: pathString)
            }
        }
        return true
    }
    
    /// Internal implementation for switching page
    internal static func switchPageInternal(appId: String, path: String) {
        if let controller = activeWindowControllers.first(where: { $0.appId == appId }),
           let viewController = controller.window?.contentViewController as? macOSLxAppViewController {
            viewController.switchPage(targetPath: path)
        }
    }
    
    /// Remove window controller from active list
    internal static func removeWindowController(_ controller: macOSLxAppWindowController) {
        activeWindowControllers.removeAll { $0 === controller }
    }
    
    /// Get active window controllers
    internal static func getActiveWindowControllers() -> [macOSLxAppWindowController] {
        return activeWindowControllers
    }

    // MARK: - LxApps Initialization

    /// Initialize LxApps system if not already initialized
    public static func initializeLxAppsIfNeeded() {
        // Check if already initialized
        if isInitialized {
            return
        }

        print("Initializing LxApps system...")

        // Use SharedLxApp.initialize() instead of duplicating the logic
        SharedLxApp.initialize()

        // Check if initialization was successful
        if SharedLxApp.getHomeLxAppId() != nil {
            print("✅ LxApps initialized successfully")
            _isInitialized = true
        } else {
            print("❌ Failed to initialize LxApps - no home app ID")
        }
    }

    /// Flag to track initialization state
    private static var _isInitialized = false

    /// Public getter for initialization state
    public static var isInitialized: Bool {
        return _isInitialized
    }
}

#endif
