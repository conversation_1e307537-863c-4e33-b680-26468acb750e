#if os(iOS)
import UIKit
import Foundation

/// iOS-specific navigation bar implementation
public class iOSNavigationBar: UIView, NavigationBarProtocol {
    
    // MARK: - Properties
    public var onBackClickListener: (() -> Void)?
    public var onAnimationEndListener: (() -> Void)?
    
    private var titleLabel: UILabel!
    private var backButton: UIButton?
    private var isBackButtonVisible: Bool = false
    
    // MARK: - Initialization
    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        backgroundColor = NavigationBarConstants.BACKGROUND_COLOR
        
        // Add bottom border
        let bottomBorder = UIView()
        bottomBorder.backgroundColor = NavigationBarConstants.BORDER_COLOR
        bottomBorder.translatesAutoresizingMaskIntoConstraints = false
        addSubview(bottomBorder)
        
        NSLayoutConstraint.activate([
            bottomBorder.leadingAnchor.constraint(equalTo: leadingAnchor),
            bottomBorder.trailingAnchor.constraint(equalTo: trailingAnchor),
            bottomBorder.bottomAnchor.constraint(equalTo: bottomAnchor),
            bottomBorder.heightAnchor.constraint(equalToConstant: 0.5)
        ])
        
        // Create title label
        titleLabel = UILabel()
        titleLabel.font = UIFont.systemFont(ofSize: NavigationBarConstants.TITLE_FONT_SIZE, weight: NavigationBarConstants.TITLE_FONT_WEIGHT)
        titleLabel.textColor = NavigationBarConstants.TITLE_COLOR
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        addSubview(titleLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.centerXAnchor.constraint(equalTo: centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: centerYAnchor)
        ])
    }
    
    // MARK: - NavigationBarProtocol Implementation
    public func setTitle(_ title: String) {
        titleLabel.text = title
    }
    
    public func setBackButtonVisible(_ visible: Bool, animated: Bool) {
        guard visible != isBackButtonVisible else { return }
        
        isBackButtonVisible = visible
        
        if visible {
            createBackButton()
        } else {
            removeBackButton()
        }
        
        if animated {
            UIView.animate(withDuration: 0.3) {
                self.layoutIfNeeded()
            } completion: { _ in
                self.onAnimationEndListener?()
            }
        } else {
            onAnimationEndListener?()
        }
    }
    
    public func updateConfig(_ config: NavigationBarConfig) {
        isHidden = config.hidden
        
        if let backgroundColor = config.navigationBarBackgroundColor {
            self.backgroundColor = backgroundColor
        }
        
        if let titleText = config.navigationBarTitleText {
            setTitle(titleText)
        }
        
        if let textStyle = config.navigationBarTextStyle {
            titleLabel.textColor = textStyle == "white" ? .white : .black
        }
    }
    
    // MARK: - Back Button Management
    private func createBackButton() {
        guard backButton == nil else { return }
        
        backButton = UIButton(type: .system)
        backButton!.setTitle("‹", for: .normal)
        backButton!.titleLabel?.font = UIFont.systemFont(ofSize: NavigationBarConstants.BACK_BUTTON_ICON_SIZE, weight: .medium)
        backButton!.setTitleColor(NavigationBarConstants.TITLE_COLOR, for: .normal)
        backButton!.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        backButton!.translatesAutoresizingMaskIntoConstraints = false
        addSubview(backButton!)
        
        NSLayoutConstraint.activate([
            backButton!.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 8),
            backButton!.centerYAnchor.constraint(equalTo: centerYAnchor),
            backButton!.widthAnchor.constraint(equalToConstant: NavigationBarConstants.BACK_BUTTON_WIDTH),
            backButton!.heightAnchor.constraint(equalToConstant: PLATFORM_NAV_BAR_HEIGHT)
        ])
    }
    
    private func removeBackButton() {
        backButton?.removeFromSuperview()
        backButton = nil
    }
    
    @objc private func backButtonTapped() {
        onBackClickListener?()
    }
}

#endif
