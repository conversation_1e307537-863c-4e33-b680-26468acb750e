#if os(iOS)
import UIKit
import Foundation

/// iOS-specific tab bar implementation
public class iOSTabBar: UIView, TabBarProtocol {
    
    // MARK: - Properties
    public var onTabSelectedListener: ((String) -> Void)?
    
    private var stackView: UIStackView!
    private var tabButtons: [UIButton] = []
    private var tabItems: [TabBarItem] = []
    private var selectedIndex: Int = 0
    
    // MARK: - Initialization
    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        backgroundColor = TabBarConfig.DEFAULT_BACKGROUND_COLOR
        
        // Add top border
        let topBorder = UIView()
        topBorder.backgroundColor = NavigationBarConstants.BORDER_COLOR
        topBorder.translatesAutoresizingMaskIntoConstraints = false
        addSubview(topBorder)
        
        NSLayoutConstraint.activate([
            topBorder.leadingAnchor.constraint(equalTo: leadingAnchor),
            topBorder.trailingAnchor.constraint(equalTo: trailingAnchor),
            topBorder.topAnchor.constraint(equalTo: topAnchor),
            topBorder.heightAnchor.constraint(equalToConstant: 0.5)
        ])
        
        // Create stack view for tab items
        stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .fill
        stackView.translatesAutoresizingMaskIntoConstraints = false
        addSubview(stackView)
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: topBorder.bottomAnchor),
            stackView.leadingAnchor.constraint(equalTo: leadingAnchor),
            stackView.trailingAnchor.constraint(equalTo: trailingAnchor),
            stackView.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }
    
    // MARK: - TabBarProtocol Implementation
    public func updateConfig(_ config: TabBarConfig) {
        isHidden = config.hidden
        
        if let backgroundColor = config.backgroundColor {
            self.backgroundColor = backgroundColor
        }
        
        tabItems = config.items
        rebuildTabButtons()
    }
    
    public func setSelectedTab(_ pagePath: String) {
        guard let index = tabItems.firstIndex(where: { $0.pagePath == pagePath }) else {
            return
        }
        
        selectedIndex = index
        updateButtonStates()
    }
    
    // MARK: - Private Methods
    private func rebuildTabButtons() {
        // Remove existing buttons
        tabButtons.forEach { $0.removeFromSuperview() }
        tabButtons.removeAll()
        
        // Create new buttons
        for (index, item) in tabItems.enumerated() {
            let button = createTabButton(for: item, at: index)
            tabButtons.append(button)
            stackView.addArrangedSubview(button)
        }
        
        updateButtonStates()
    }
    
    private func createTabButton(for item: TabBarItem, at index: Int) -> UIButton {
        let button = UIButton(type: .custom)
        button.tag = index
        button.addTarget(self, action: #selector(tabButtonTapped(_:)), for: .touchUpInside)
        
        // Create vertical stack for icon and text
        let containerView = UIView()
        containerView.isUserInteractionEnabled = false
        button.addSubview(containerView)
        containerView.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            containerView.centerXAnchor.constraint(equalTo: button.centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: button.centerYAnchor)
        ])
        
        // Icon (placeholder for now - you can implement image loading later)
        let iconView = UIView()
        iconView.backgroundColor = TabBarConfig.DEFAULT_COLOR
        iconView.layer.cornerRadius = TabBarConstants.ICON_SIZE / 2
        iconView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(iconView)
        
        // Text label
        let textLabel = UILabel()
        textLabel.text = item.text
        textLabel.font = UIFont.systemFont(ofSize: TabBarConstants.ITEM_FONT_SIZE)
        textLabel.textColor = TabBarConfig.DEFAULT_COLOR
        textLabel.textAlignment = .center
        textLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(textLabel)
        
        NSLayoutConstraint.activate([
            iconView.topAnchor.constraint(equalTo: containerView.topAnchor),
            iconView.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            iconView.widthAnchor.constraint(equalToConstant: TabBarConstants.ICON_SIZE),
            iconView.heightAnchor.constraint(equalToConstant: TabBarConstants.ICON_SIZE),
            
            textLabel.topAnchor.constraint(equalTo: iconView.bottomAnchor, constant: TabBarConstants.ITEM_SPACING),
            textLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            textLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            textLabel.bottomAnchor.constraint(equalTo: containerView.bottomAnchor)
        ])
        
        return button
    }
    
    private func updateButtonStates() {
        for (index, button) in tabButtons.enumerated() {
            let isSelected = index == selectedIndex
            
            // Update colors based on selection state
            if let containerView = button.subviews.first,
               let iconView = containerView.subviews.first,
               let textLabel = containerView.subviews.last as? UILabel {
                
                let color = isSelected ? TabBarConfig.DEFAULT_SELECTED_COLOR : TabBarConfig.DEFAULT_COLOR
                iconView.backgroundColor = color
                textLabel.textColor = color
            }
        }
    }
    
    @objc private func tabButtonTapped(_ sender: UIButton) {
        let index = sender.tag
        guard index < tabItems.count else { return }
        
        selectedIndex = index
        updateButtonStates()
        
        let selectedItem = tabItems[index]
        onTabSelectedListener?(selectedItem.pagePath)
    }
}

#endif
