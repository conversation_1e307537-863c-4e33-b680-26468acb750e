#if os(iOS)
import UIKit
import WebKit
import os.log

/// iOS-specific LxApp view controller
@MainActor
public class iOSLxAppViewController: SharedLxAppViewController {
    private static let log = OSLog(subsystem: "LingXia", category: "iOSLxAppViewController")
    
    // UI Element Tags
    private static let CAPSULE_BUTTON_TAG = 9999
    private static let CURRENT_WEBVIEW_CONTAINER_TAG = 999
    private static let OLD_WEBVIEW_CONTAINER_TAG = 998
    
    private var isDestroyed = false
    private var pendingWebViewSetup = false
    
    public override func viewDidLoad() {
        super.viewDidLoad()
        
        // Configure transparent system bars
        iOSLxApp.configureTransparentSystemBars(viewController: self)
    }
    
    public override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // Hide navigation bar if present
        navigationController?.setNavigationBarHidden(true, animated: false)
    }
    
    public override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        if pendingWebViewSetup {
            pendingWebViewSetup = false
            loadInitialPage()
        }
    }
    
    // MARK: - UI Setup Implementation
    internal override func setupUI() {
        view.backgroundColor = .systemBackground
        
        // Create root container
        rootContainer = UIView()
        rootContainer.backgroundColor = .systemBackground
        rootContainer.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(rootContainer)
        
        // Create status bar background
        statusBarBackground = UIView()
        statusBarBackground.backgroundColor = .systemBackground
        statusBarBackground.translatesAutoresizingMaskIntoConstraints = false
        rootContainer.addSubview(statusBarBackground)
        
        // Create web view container
        webViewContainer = UIView()
        webViewContainer.backgroundColor = .systemBackground
        webViewContainer.tag = Self.CURRENT_WEBVIEW_CONTAINER_TAG
        webViewContainer.translatesAutoresizingMaskIntoConstraints = false
        rootContainer.addSubview(webViewContainer)
        
        // Setup constraints
        setupConstraints()
        
        // Create navigation and tab bars
        navigationBar = createNavigationBar()
        tabBar = createTabBar()
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // Root container
            rootContainer.topAnchor.constraint(equalTo: view.topAnchor),
            rootContainer.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            rootContainer.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            rootContainer.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            // Status bar background
            statusBarBackground.topAnchor.constraint(equalTo: rootContainer.topAnchor),
            statusBarBackground.leadingAnchor.constraint(equalTo: rootContainer.leadingAnchor),
            statusBarBackground.trailingAnchor.constraint(equalTo: rootContainer.trailingAnchor),
            statusBarBackground.heightAnchor.constraint(equalToConstant: PLATFORM_STATUS_BAR_HEIGHT),
            
            // Web view container
            webViewContainer.topAnchor.constraint(equalTo: statusBarBackground.bottomAnchor),
            webViewContainer.leadingAnchor.constraint(equalTo: rootContainer.leadingAnchor),
            webViewContainer.trailingAnchor.constraint(equalTo: rootContainer.trailingAnchor),
            webViewContainer.bottomAnchor.constraint(equalTo: rootContainer.bottomAnchor)
        ])
    }
    
    // MARK: - Navigation and Tab Bar Creation
    internal override func createNavigationBar() -> NavigationBarProtocol? {
        let navBar = iOSNavigationBar()
        navBar.translatesAutoresizingMaskIntoConstraints = false
        rootContainer.addSubview(navBar)
        
        NSLayoutConstraint.activate([
            navBar.topAnchor.constraint(equalTo: statusBarBackground.bottomAnchor),
            navBar.leadingAnchor.constraint(equalTo: rootContainer.leadingAnchor),
            navBar.trailingAnchor.constraint(equalTo: rootContainer.trailingAnchor),
            navBar.heightAnchor.constraint(equalToConstant: PLATFORM_NAV_BAR_HEIGHT)
        ])
        
        // Update web view container constraints
        webViewContainer.topAnchor.constraint(equalTo: navBar.bottomAnchor).isActive = true
        
        return navBar
    }
    
    internal override func createTabBar() -> TabBarProtocol? {
        let tabBarView = iOSTabBar()
        tabBarView.translatesAutoresizingMaskIntoConstraints = false
        rootContainer.addSubview(tabBarView)
        
        NSLayoutConstraint.activate([
            tabBarView.leadingAnchor.constraint(equalTo: rootContainer.leadingAnchor),
            tabBarView.trailingAnchor.constraint(equalTo: rootContainer.trailingAnchor),
            tabBarView.bottomAnchor.constraint(equalTo: rootContainer.bottomAnchor),
            tabBarView.heightAnchor.constraint(equalToConstant: PLATFORM_TAB_BAR_HEIGHT)
        ])
        
        // Update web view container constraints
        webViewContainer.bottomAnchor.constraint(equalTo: tabBarView.topAnchor).isActive = true
        
        return tabBarView
    }
    
    // MARK: - WebView Management
    internal override func setupWebViewConstraints(_ webView: WKWebView) {
        webView.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            webView.topAnchor.constraint(equalTo: webViewContainer.topAnchor),
            webView.leadingAnchor.constraint(equalTo: webViewContainer.leadingAnchor),
            webView.trailingAnchor.constraint(equalTo: webViewContainer.trailingAnchor),
            webView.bottomAnchor.constraint(equalTo: webViewContainer.bottomAnchor)
        ])
    }
    
    // MARK: - App Management
    internal override func closeApp() {
        guard !isDestroyed else { return }
        isDestroyed = true
        
        if presentingViewController != nil {
            dismiss(animated: true)
        } else {
            // Handle root view controller case
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let window = windowScene.windows.first {
                // You might want to navigate to a default view controller here
                window.rootViewController = UIViewController()
            }
        }
    }
}

#endif
