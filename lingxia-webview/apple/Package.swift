// swift-tools-version: 6.0

import PackageDescription
import Foundation

// Use absolute paths to the library files
let iosLibraryPath = "/Users/<USER>/github/LingXia/target/aarch64-apple-ios/release/liblingxia.a"

// Determine macOS library path based on architecture
#if arch(arm64)
let macosLibraryPath = "/Users/<USER>/github/LingXia/target/aarch64-apple-darwin/release/liblingxia.a"
#else
let macosLibraryPath = "/Users/<USER>/github/LingXia/target/x86_64-apple-darwin/release/liblingxia.a"
#endif

let package = Package(
    name: "lingxia",
    platforms: [
        .iOS(.v17),
        .macOS(.v11)
    ],
    products: [
        .library(
            name: "lingxia",
            targets: ["lingxia"]
        ),
    ],
    targets: [
        .systemLibrary(
            name: "CLingXiaFFI",
            path: "Sources/generated",
            pkgConfig: nil,
            providers: nil
        ),
        .target(
            name: "lingxia",
            dependencies: ["CLingXiaFFI"],
            path: "Sources",
            publicHeadersPath: nil,
            cSettings: [
                .headerSearchPath("generated"),
            ],
            linkerSettings: [
                .unsafeFlags([iosLibraryPath], .when(platforms: [.iOS])),
                .unsafeFlags([macosLibraryPath], .when(platforms: [.macOS])),
                .linkedFramework("JavaScriptCore"),
                .linkedFramework("WebKit"),
            ]
        ),
    ]
)
