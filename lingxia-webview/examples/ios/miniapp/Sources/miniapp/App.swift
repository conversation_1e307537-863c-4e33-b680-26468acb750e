import SwiftUI
import UIKit
import Foundation
import lingxia
import os.log

public struct ContentView: View {
    // Use a global flag instead of @State to avoid SwiftUI update cycle issues
    private static var hasInitialized = false

    public var body: some View {
        Color.clear
            .onAppear {
                if !Self.hasInitialized {
                    Self.hasInitialized = true
                    LxApp.initialize(mode: .replaceRoot)

                    // Install test miniapp for openLxApp testing
                    installTestLxApp()

                    LxApp.openHomeLxApp()
                }
            }
    }
}

/// Install test miniapp for openLxApp functionality testing
/// This copies the installed homeminiapp from data directory to create a test miniapp
private func installTestLxApp() {
    let log = OSLog(subsystem: "LingXia.LxApp", category: "TestInstall")

    os_log("Installing test miniapp: 95dc2dcfcccc191", log: log, type: .info)

    // Get the documents directory path
    guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
        os_log("Failed to get documents directory", log: log, type: .error)
        return
    }

    // Construct paths for source and destination
    let lingxiaDir = documentsPath.appendingPathComponent("lingxia")
    let miniappsDir = lingxiaDir.appendingPathComponent("lxapps")
    let versionsDir = lingxiaDir.appendingPathComponent("versions")

    let sourceDir = miniappsDir.appendingPathComponent("homelxapp")
    let destDir = miniappsDir.appendingPathComponent("95dc2dcfcccc191")
    let sourceVersionFile = versionsDir.appendingPathComponent("homelxapp.txt")
    let destVersionFile = versionsDir.appendingPathComponent("95dc2dcfcccc191.txt")

    let fileManager = FileManager.default

    do {
        // Check if source homeminiapp exists
        guard fileManager.fileExists(atPath: sourceDir.path) else {
            os_log("Source homeminiapp not found at: %@", log: log, type: .error, sourceDir.path)
            return
        }

        // Remove destination if it already exists
        if fileManager.fileExists(atPath: destDir.path) {
            try fileManager.removeItem(at: destDir)
            os_log("Removed existing test miniapp directory", log: log, type: .info)
        }

        // Copy the entire homeminiapp directory to create test miniapp
        try fileManager.copyItem(at: sourceDir, to: destDir)
        os_log("Successfully copied homeminiapp to test miniapp directory", log: log, type: .info)

        // Copy version file if it exists
        if fileManager.fileExists(atPath: sourceVersionFile.path) {
            if fileManager.fileExists(atPath: destVersionFile.path) {
                try fileManager.removeItem(at: destVersionFile)
            }
            try fileManager.copyItem(at: sourceVersionFile, to: destVersionFile)
            os_log("Successfully copied version file", log: log, type: .info)
        }

        os_log("Test miniapp 95dc2dcfcccc191 installation completed successfully", log: log, type: .info)

    } catch {
        os_log("Failed to install test miniapp: %@", log: log, type: .error, error.localizedDescription)
    }
}

@main
public struct LxAppApp: App {
    public init() { }

    public var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}
