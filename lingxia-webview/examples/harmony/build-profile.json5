{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.cer",
          "keyAlias": "debugKey",
          "keyPassword": "0000001BB9D66BF867CE30E406FB12F236D7D960D9FD37F7CFD731E5F8B51BA313340342DA51E68DF3F32B",
          "profile": "/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.p12",
          "storePassword": "0000001B66D55D9B0EA16D1B413FBBCAE6D6C7B270B3280E81F1B39261DCAFB0ACCB154A93851E2E7BD33D"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "targetSdkVersion": "5.0.5(17)",
        "compatibleSdkVersion": "5.0.5(17)",
        "runtimeOS": "HarmonyOS",
        "buildOption": {
          "strictMode": {
            "caseSensitiveCheck": true,
            "useNormalizedOHMUrl": true
          }
        }
      }
    ],
    "buildModeSet": [
      {
        "name": "debug",
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "lingxia",
      "srcPath": "./../../harmony/lingxia",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}