import { LxApp, LxAppNavigation } from 'lingxia';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { fileIo } from '@kit.CoreFileKit';

const DOMAIN = 0x0000;
const TAG = 'LingXia.TestInstall';

/**
 * Install test miniapp for openLxApp functionality testing
 * This copies the installed homeminiapp from data directory to create a test miniapp
 */
function installTestLxApp(): void {
  hilog.info(DOMAIN, TAG, 'Installing test miniapp: 95dc2dcfcccc191');

  try {
    const context = getContext();
    const appContext = context.getApplicationContext();
    const dataDir = appContext.filesDir;

    // Construct paths for source and destination
    const lingxiaDir = `${dataDir}/lingxia`;
    const miniappsDir = `${lingxiaDir}/lxapps`;
    const versionsDir = `${lingxiaDir}/versions`;

    const sourceDir = `${miniappsDir}/homelxapp`;

    // Use the exact hash from the error message for the test miniapp
    const testAppId = 'testminiapp';
    const hashedDirName = '95dc2dcfcccc191'; // Direct hash from error message

    const destDir = `${miniappsDir}/${hashedDirName}`;
    const sourceVersionFile = `${versionsDir}/homelxapp.txt`;
    const destVersionFile = `${versionsDir}/${testAppId}.txt`; // Version file uses original appId

    hilog.info(DOMAIN, TAG, `Using hash directory: ${hashedDirName} for appId: ${testAppId}`);

    // Check if source homeminiapp exists
    if (!fileIo.accessSync(sourceDir)) {
      hilog.error(DOMAIN, TAG, `Source homeminiapp not found at: ${sourceDir}`);
      return;
    }

    // Remove destination if it already exists
    if (fileIo.accessSync(destDir)) {
      fileIo.rmdirSync(destDir);
      hilog.info(DOMAIN, TAG, `Removed existing test miniapp directory: ${destDir}`);
    }

    // Create destination directory
    fileIo.mkdirSync(destDir);

    // Copy the entire homeminiapp directory to create test miniapp
    copyDirectory(sourceDir, destDir);
    hilog.info(DOMAIN, TAG, `Successfully copied homeminiapp to test miniapp directory: ${destDir}`);

    // Copy version file if it exists
    if (fileIo.accessSync(sourceVersionFile)) {
      if (fileIo.accessSync(destVersionFile)) {
        fileIo.unlinkSync(destVersionFile);
      }
      fileIo.copyFileSync(sourceVersionFile, destVersionFile);
      hilog.info(DOMAIN, TAG, 'Successfully copied version file');
    }

    hilog.info(DOMAIN, TAG, `Test miniapp ${testAppId} installation completed successfully in directory: ${hashedDirName}`);

  } catch (error) {
    hilog.error(DOMAIN, TAG, `Failed to install test miniapp: ${JSON.stringify(error)}`);
  }
}

/**
 * Copy directory recursively
 * @param src - Source directory path
 * @param dest - Destination directory path
 */
function copyDirectory(src: string, dest: string): void {
  try {
    const entries = fileIo.listFileSync(src);

    for (const entry of entries) {
      const srcPath = `${src}/${entry}`;
      const destPath = `${dest}/${entry}`;

      const stat = fileIo.statSync(srcPath);
      if (stat.isDirectory()) {
        fileIo.mkdirSync(destPath);
        copyDirectory(srcPath, destPath);
      } else {
        fileIo.copyFileSync(srcPath, destPath);
      }
    }
  } catch (error) {
    hilog.error(DOMAIN, TAG, `Failed to copy directory from ${src} to ${dest}: ${JSON.stringify(error)}`);
    return; // Don't throw, just return
  }
}

@Entry
@Component
struct Index {
  aboutToAppear() {
    // Install test miniapp for openLxApp testing
    installTestLxApp();

    LxApp.openHomeLxApp();
  }

  build() {
    Column() {
      LxAppNavigation({ autoOpenHome: false })
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Color.Transparent)
  }
}
