client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "LingXiaDemo-x86_64-apple-macosx15.0-debug.exe": ["<LingXiaDemo-x86_64-apple-macosx15.0-debug.exe>"]
  "LingXiaDemo-x86_64-apple-macosx15.0-debug.module": ["<LingXiaDemo-x86_64-apple-macosx15.0-debug.module>"]
  "PackageStructure": ["<PackageStructure>"]
  "lingxia-x86_64-apple-macosx15.0-debug.module": ["<lingxia-x86_64-apple-macosx15.0-debug.module>"]
  "main": ["<LingXiaDemo-x86_64-apple-macosx15.0-debug.exe>","<LingXiaDemo-x86_64-apple-macosx15.0-debug.module>"]
  "test": ["<LingXiaDemo-x86_64-apple-macosx15.0-debug.exe>","<LingXiaDemo-x86_64-apple-macosx15.0-debug.module>"]
default: "main"
nodes:
  "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/Sources/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo":
    is-mutated: true
commands:
  "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo-entitlement.plist":
    tool: write-auxiliary-file
    inputs: ["<entitlement-plist>","<com.apple.security.get-task-allow>"]
    outputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo-entitlement.plist"]
    description: "Write auxiliary file /Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo-entitlement.plist"

  "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/Sources/main.swift","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/DerivedSources/resource_bundle_accessor.swift"]
    outputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/sources"]
    description: "Write auxiliary file /Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/sources"

  "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/main.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/resource_bundle_accessor.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/ColorExtensions.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedLxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedLxAppViewController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedNavigationBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedTabBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedWebView.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSNavigationBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSTabBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppWindowController.swift.o"]
    outputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.product/Objects.LinkFileList"

  "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo_LingXiaDemo.bundle/Resources":
    tool: copy-tool
    inputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/Sources/Resources/"]
    outputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo_LingXiaDemo.bundle/Resources/"]
    description: "Copying /Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/Sources/Resources"

  "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/ColorExtensions.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/LxApp.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/PlatformTypes.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedLxApp.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedLxAppViewController.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedNavigationBar.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedTabBar.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedWebView.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/SwiftBridgeCore.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSLxApp.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSLxAppViewController.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSNavigationBar.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSTabBar.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxApp.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxAppViewController.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxAppWindowController.swift"]
    outputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/sources"]
    description: "Write auxiliary file /Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/sources"

  "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"

  "<LingXiaDemo-x86_64-apple-macosx15.0-debug.exe>":
    tool: phony
    inputs: ["<LingXiaDemo-x86_64-apple-macosx15.0-debug.exe-CodeSigning>"]
    outputs: ["<LingXiaDemo-x86_64-apple-macosx15.0-debug.exe>"]

  "<LingXiaDemo-x86_64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/main.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/resource_bundle_accessor.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules/LingXiaDemo.swiftmodule"]
    outputs: ["<LingXiaDemo-x86_64-apple-macosx15.0-debug.module>"]

  "<lingxia-x86_64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/ColorExtensions.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedLxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedLxAppViewController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedNavigationBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedTabBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedWebView.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSNavigationBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSTabBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppWindowController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules/lingxia.swiftmodule"]
    outputs: ["<lingxia-x86_64-apple-macosx15.0-debug.module>"]

  "C.LingXiaDemo-x86_64-apple-macosx15.0-debug.exe":
    tool: shell
    inputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/main.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/resource_bundle_accessor.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/ColorExtensions.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedLxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedLxAppViewController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedNavigationBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedTabBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedWebView.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSNavigationBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSTabBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppWindowController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo"]
    description: "Linking ./.build/x86_64-apple-macosx/debug/LingXiaDemo"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug","-o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo","-module-name","LingXiaDemo","-Xlinker","-no_warn_duplicate_libraries","-emit-executable","-Xlinker","-alias","-Xlinker","_LingXiaDemo_main","-Xlinker","_main","-Xlinker","-rpath","-Xlinker","@loader_path","@/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.product/Objects.LinkFileList","-Xlinker","-rpath","-Xlinker","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.5/macosx","-target","x86_64-apple-macosx11.0","-framework","JavaScriptCore","-framework","WebKit","/Users/<USER>/github/LingXia/target/x86_64-apple-darwin/release/liblingxia.a","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules/LingXiaDemo.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules/lingxia.swiftmodule","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "C.LingXiaDemo-x86_64-apple-macosx15.0-debug.exe-entitlements":
    tool: shell
    inputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo-entitlement.plist"]
    outputs: ["<LingXiaDemo-x86_64-apple-macosx15.0-debug.exe-CodeSigning>"]
    description: "Applying debug entitlements to ./.build/x86_64-apple-macosx/debug/LingXiaDemo"
    args: ["codesign","--force","--sign","-","--entitlements","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo-entitlement.plist","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo"]

  "C.LingXiaDemo-x86_64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/Sources/main.swift","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/DerivedSources/resource_bundle_accessor.swift","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","<LingXiaDemo-x86_64-apple-macosx15.0-debug.module-resources>","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules/lingxia.swiftmodule","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/sources"]
    outputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/main.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/resource_bundle_accessor.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules/LingXiaDemo.swiftmodule"]
    description: "Compiling Swift Module 'LingXiaDemo' (2 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","LingXiaDemo","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules/LingXiaDemo.swiftmodule","-output-file-map","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/output-file-map.json","-incremental","-c","@/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/sources","-I","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules","-target","x86_64-apple-macosx11.0","-enable-batch-mode","-index-store-path","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j8","-DSWIFT_PACKAGE","-DDEBUG","-Xcc","-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/module.modulemap","-module-cache-path","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/ModuleCache","-parseable-output","-Xfrontend","-entry-point-function-name","-Xfrontend","LingXiaDemo_main","-color-diagnostics","-swift-version","6","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","miniapp"]

  "C.lingxia-x86_64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/ColorExtensions.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/LxApp.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/PlatformTypes.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedLxApp.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedLxAppViewController.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedNavigationBar.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedTabBar.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedWebView.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/SwiftBridgeCore.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSLxApp.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSLxAppViewController.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSNavigationBar.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSTabBar.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxApp.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxAppViewController.swift","/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxAppWindowController.swift","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/sources"]
    outputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/ColorExtensions.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedLxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedLxAppViewController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedNavigationBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedTabBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedWebView.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSNavigationBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSTabBar.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxApp.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppWindowController.swift.o","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules/lingxia.swiftmodule"]
    description: "Compiling Swift Module 'lingxia' (17 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","lingxia","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules/lingxia.swiftmodule","-output-file-map","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/sources","-I","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules","-target","x86_64-apple-macosx11.0","-enable-batch-mode","-index-store-path","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j8","-DSWIFT_PACKAGE","-DDEBUG","-Xcc","-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/module.modulemap","-module-cache-path","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/lingxia-Swift.h","-color-diagnostics","-swift-version","6","-Xcc","-I/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","apple"]

  "LingXiaDemo-x86_64-apple-macosx15.0-debug.module-resources":
    tool: phony
    inputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo_LingXiaDemo.bundle/Resources/"]
    outputs: ["<LingXiaDemo-x86_64-apple-macosx15.0-debug.module-resources>"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/Sources/","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/Package.swift","/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

