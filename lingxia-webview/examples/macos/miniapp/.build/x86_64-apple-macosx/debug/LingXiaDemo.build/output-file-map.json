{"": {"swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/master.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/Sources/main.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/main.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/main.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/main~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/main.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/DerivedSources/resource_bundle_accessor.swift": {"dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/resource_bundle_accessor.d", "object": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/resource_bundle_accessor.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/resource_bundle_accessor~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/resource_bundle_accessor.swiftdeps"}}