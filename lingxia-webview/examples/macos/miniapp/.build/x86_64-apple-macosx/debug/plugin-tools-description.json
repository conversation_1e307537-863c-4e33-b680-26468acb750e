{"builtTestProducts": [], "copyCommands": {"/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo_LingXiaDemo.bundle/Resources": {"inputs": [{"kind": "directory", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/Sources/Resources"}], "outputs": [{"kind": "directory", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo_LingXiaDemo.bundle/Resources"}]}}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.LingXiaDemo-x86_64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/sources", "importPath": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/Sources/main.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/DerivedSources/resource_bundle_accessor.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "virtual", "name": "<LingXiaDemo-x86_64-apple-macosx15.0-debug.module-resources>"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules/lingxia.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/sources"}], "isLibrary": false, "moduleName": "LingXiaDemo", "moduleOutputPath": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules/LingXiaDemo.swiftmodule", "objects": ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/main.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/resource_bundle_accessor.swift.o"], "otherArguments": ["-target", "x86_64-apple-macosx11.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/module.modulemap", "-module-cache-path", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "LingXiaDemo_main", "-color-diagnostics", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "miniapp"], "outputFileMapPath": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/main.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/resource_bundle_accessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules/LingXiaDemo.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/Sources/main.swift", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/DerivedSources/resource_bundle_accessor.swift"], "tempsPath": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build", "wholeModuleOptimization": false}, "C.lingxia-x86_64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/sources", "importPath": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/ColorExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/LxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/PlatformTypes.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedLxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedLxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedNavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedTabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedWebView.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/SwiftBridgeCore.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSLxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSLxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSNavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSTabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxAppWindowController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/sources"}], "isLibrary": true, "moduleName": "lingxia", "moduleOutputPath": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules/lingxia.swiftmodule", "objects": ["/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/ColorExtensions.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedLxApp.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedLxAppViewController.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedNavigationBar.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedTabBar.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedWebView.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxApp.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxAppViewController.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSNavigationBar.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSTabBar.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxApp.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppViewController.swift.o", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppWindowController.swift.o"], "otherArguments": ["-target", "x86_64-apple-macosx11.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/module.modulemap", "-module-cache-path", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/lingxia-Swift.h", "-color-diagnostics", "-swift-version", "6", "-Xcc", "-I/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "apple"], "outputFileMapPath": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/ColorExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedLxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedLxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedNavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedTabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedWebView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSNavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSTabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppWindowController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules/lingxia.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/ColorExtensions.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/LxApp.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/PlatformTypes.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedLxApp.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedLxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedNavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedTabBar.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedWebView.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/SwiftBridgeCore.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSLxApp.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSLxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSNavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSTabBar.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxApp.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxAppWindowController.swift"], "tempsPath": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"LingXiaDemo": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "LingXiaDemo", "-package-name", "miniapp", "-incremental", "-c", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/Sources/main.swift", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/DerivedSources/resource_bundle_accessor.swift", "-I", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules", "-target", "x86_64-apple-macosx11.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/module.modulemap", "-module-cache-path", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "LingXiaDemo_main", "-color-diagnostics", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "miniapp", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "lingxia": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "lingxia", "-package-name", "apple", "-incremental", "-c", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/ColorExtensions.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/LxApp.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/PlatformTypes.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedLxApp.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedLxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedNavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedTabBar.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedWebView.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/SwiftBridgeCore.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSLxApp.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSLxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSNavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSTabBar.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxApp.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxAppWindowController.swift", "-I", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/Modules", "-target", "x86_64-apple-macosx11.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/module.modulemap", "-module-cache-path", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/lingxia-Swift.h", "-color-diagnostics", "-swift-version", "6", "-Xcc", "-I/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "apple", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"LingXiaDemo": ["lingxia", "CLingXiaFFI"], "lingxia": ["CLingXiaFFI"]}, "testDiscoveryCommands": {}, "testEntryPointCommands": {}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo-entitlement.plist": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<entitlement-plist>"}, {"kind": "virtual", "name": "<com.apple.security.get-task-allow>"}], "outputFilePath": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo-entitlement.plist"}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/Sources/main.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/DerivedSources/resource_bundle_accessor.swift"}], "outputFilePath": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/sources"}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/main.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/resource_bundle_accessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/ColorExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedLxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedLxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedNavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedTabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SharedWebView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSNavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/iOSTabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppWindowController.swift.o"}], "outputFilePath": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/LingXiaDemo.product/Objects.LinkFileList"}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/ColorExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/LxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/PlatformTypes.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedLxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedLxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedNavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedTabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/SharedWebView.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/generated/SwiftBridgeCore.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSLxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSLxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSNavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/iOS/iOSTabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-webview/apple/Sources/macOS/macOSLxAppWindowController.swift"}], "outputFilePath": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/lingxia.build/sources"}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/github/LingXia/lingxia-webview/examples/macos/miniapp/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}}}