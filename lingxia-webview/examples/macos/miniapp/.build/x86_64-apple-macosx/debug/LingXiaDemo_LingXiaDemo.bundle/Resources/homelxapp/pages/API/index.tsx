<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>API</title>
    <script type="module" src="./view.js"></script>
    <link rel="stylesheet" href="./index.css">
  </head>
  <body>
    <div id="root"></div>
  <script>
window.__PAGE_FUNCTIONS = ["openLxApp"];

// Generate bridge functions
window.__PAGE_FUNCTIONS.forEach(function(funcName) {
  window[funcName] = function(...args) {
    return window.LingXiaBridge.call(funcName, args.length === 1 ? args[0] : args);
  };
});
</script>
</body>
</html>
