---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/WebKit.swiftmodule/x86_64-apple-macos.swiftmodule'
dependencies:
  - mtime:           1746401650000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/WebKit.swiftmodule/x86_64-apple-macos.swiftmodule'
    size:            51384
  - mtime:           1745034367000000000
    path:            'usr/lib/swift/Swift.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1912950
    sdk_relative:    true
  - mtime:           1745035158000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1745035390000000000
    path:            'usr/lib/swift/_errno.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            3890
    sdk_relative:    true
  - mtime:           1745035397000000000
    path:            'usr/lib/swift/_time.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1065
    sdk_relative:    true
  - mtime:           1745035419000000000
    path:            'usr/lib/swift/_signal.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1101
    sdk_relative:    true
  - mtime:           1745035419000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1102
    sdk_relative:    true
  - mtime:           1745035413000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1513
    sdk_relative:    true
  - mtime:           1745035426000000000
    path:            'usr/lib/swift/unistd.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            853
    sdk_relative:    true
  - mtime:           1745035390000000000
    path:            'usr/lib/swift/_math.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22628
    sdk_relative:    true
  - mtime:           1745034431000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            5775
    sdk_relative:    true
  - mtime:           1745035444000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            18255
    sdk_relative:    true
  - mtime:           1745035821000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            230631
    sdk_relative:    true
  - mtime:           1745036006000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22908
    sdk_relative:    true
  - mtime:           1745036535000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            167834
    sdk_relative:    true
  - mtime:           1745043435000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1745030861000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1745036485000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            6610
    sdk_relative:    true
  - mtime:           1745036728000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            57170
    sdk_relative:    true
  - mtime:           1745035965000000000
    path:            'usr/lib/swift/Distributed.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22851
    sdk_relative:    true
  - mtime:           1745037002000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22953
    sdk_relative:    true
  - mtime:           1745376776000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1745038091000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1745045809000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1745037658000000000
    path:            'usr/lib/swift/XPC.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            33654
    sdk_relative:    true
  - mtime:           1745038520000000000
    path:            'usr/lib/swift/IOKit.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            3651
    sdk_relative:    true
  - mtime:           1745035831000000000
    path:            'usr/lib/swift/Observation.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            3489
    sdk_relative:    true
  - mtime:           1745036593000000000
    path:            'usr/lib/swift/System.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            95504
    sdk_relative:    true
  - mtime:           1746092899000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            995545
    sdk_relative:    true
  - mtime:           1745803780000000000
    path:            'System/Library/Frameworks/Network.framework/Headers/Network.apinotes'
    size:            213
    sdk_relative:    true
  - mtime:           1745803878000000000
    path:            'System/Library/Frameworks/Network.framework/Modules/Network.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            79184
    sdk_relative:    true
  - mtime:           1745047476000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            52901
    sdk_relative:    true
  - mtime:           1745040352000000000
    path:            'System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes'
    size:            1662
    sdk_relative:    true
  - mtime:           1746092467000000000
    path:            'System/Library/Frameworks/ApplicationServices.framework/Headers/ApplicationServices.apinotes'
    size:            2012
    sdk_relative:    true
  - mtime:           1745047406000000000
    path:            'System/Library/Frameworks/CoreData.framework/Headers/CoreData.apinotes'
    size:            7789
    sdk_relative:    true
  - mtime:           1743191765000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            80245
    sdk_relative:    true
  - mtime:           1745044942000000000
    path:            'System/Library/Frameworks/CoreImage.framework/Headers/CoreImage.apinotes'
    size:            36883
    sdk_relative:    true
  - mtime:           1745046206000000000
    path:            'System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes'
    size:            7428
    sdk_relative:    true
  - mtime:           1746093410000000000
    path:            'System/Library/Frameworks/AppKit.framework/Headers/AppKit.apinotes'
    size:            384123
    sdk_relative:    true
  - mtime:           1746329023000000000
    path:            'System/Library/Frameworks/WebKit.framework/Headers/WebKit.apinotes'
    size:            4308
    sdk_relative:    true
  - mtime:           1745039975000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            53548
    sdk_relative:    true
  - mtime:           1745629030000000000
    path:            'System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            21906
    sdk_relative:    true
  - mtime:           1745039870000000000
    path:            'System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes'
    size:            1666
    sdk_relative:    true
  - mtime:           1745040477000000000
    path:            'usr/lib/swift/UniformTypeIdentifiers.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            20610
    sdk_relative:    true
  - mtime:           1745040441000000000
    path:            'System/Library/Frameworks/CoreText.framework/Modules/CoreText.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1581
    sdk_relative:    true
  - mtime:           1745038028000000000
    path:            'System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            42738
    sdk_relative:    true
  - mtime:           1745038179000000000
    path:            'usr/lib/swift/Metal.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            25175
    sdk_relative:    true
  - mtime:           1745041263000000000
    path:            'usr/lib/swift/QuartzCore.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1812
    sdk_relative:    true
  - mtime:           1745042384000000000
    path:            'System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            21596
    sdk_relative:    true
  - mtime:           1745041253000000000
    path:            'usr/lib/swift/CoreImage.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            738
    sdk_relative:    true
  - mtime:           1745035158000000000
    path:            'usr/include/os.apinotes'
    size:            1658
    sdk_relative:    true
  - mtime:           1745036886000000000
    path:            'usr/lib/swift/os.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            108108
    sdk_relative:    true
  - mtime:           1745043128000000000
    path:            'System/Library/Frameworks/CoreTransferable.framework/Modules/CoreTransferable.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22071
    sdk_relative:    true
  - mtime:           1745037953000000000
    path:            'usr/lib/swift/DataDetection.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            736
    sdk_relative:    true
  - mtime:           1745041112000000000
    path:            'System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            10920
    sdk_relative:    true
  - mtime:           1745038238000000000
    path:            'usr/lib/swift/OSLog.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1298
    sdk_relative:    true
  - mtime:           1746093522000000000
    path:            'System/Library/Frameworks/AppKit.framework/Modules/AppKit.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            91781
    sdk_relative:    true
  - mtime:           1746328845000000000
    path:            'System/Library/Frameworks/WebKit.framework/Modules/WebKit.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            4901
    sdk_relative:    true
version:         1
...
