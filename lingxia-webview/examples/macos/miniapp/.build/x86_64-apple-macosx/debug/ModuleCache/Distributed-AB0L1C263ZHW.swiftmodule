---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Distributed.swiftmodule/x86_64-apple-macos.swiftmodule'
dependencies:
  - mtime:           1746401337000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Distributed.swiftmodule/x86_64-apple-macos.swiftmodule'
    size:            84312
  - mtime:           1745035158000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1745034367000000000
    path:            'usr/lib/swift/Swift.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1912950
    sdk_relative:    true
  - mtime:           1745035390000000000
    path:            'usr/lib/swift/_errno.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            3890
    sdk_relative:    true
  - mtime:           1745035397000000000
    path:            'usr/lib/swift/_time.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1065
    sdk_relative:    true
  - mtime:           1745035419000000000
    path:            'usr/lib/swift/_signal.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1101
    sdk_relative:    true
  - mtime:           1745035419000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1102
    sdk_relative:    true
  - mtime:           1745035413000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1513
    sdk_relative:    true
  - mtime:           1745035426000000000
    path:            'usr/lib/swift/unistd.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            853
    sdk_relative:    true
  - mtime:           1745035390000000000
    path:            'usr/lib/swift/_math.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22628
    sdk_relative:    true
  - mtime:           1745034431000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            5775
    sdk_relative:    true
  - mtime:           1745035444000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            18255
    sdk_relative:    true
  - mtime:           1745035821000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            230631
    sdk_relative:    true
  - mtime:           1745035965000000000
    path:            'usr/lib/swift/Distributed.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22851
    sdk_relative:    true
version:         1
...
