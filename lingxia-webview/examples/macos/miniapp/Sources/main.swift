import Cocoa
import lingxia
import os.log



@MainActor
class AppDelegate: NSObject, NSApplicationDelegate {
    private static var hasInitialized = false
    private let log = OSLog(subsystem: "LingXia.LxApp.macOS", category: "App")

    func applicationDidFinishLaunching(_ aNotification: Notification) {
        print("DEBUG: AppDelegate: applicationDidFinishLaunching called")
        // Ensure app activation
        NSApp.setActivationPolicy(.regular)
        NSApp.activate(ignoringOtherApps: true)

        if !Self.hasInitialized {
            Self.hasInitialized = true

            print("DEBUG: AppDelegate: Initializing LxApps...")
            // Initialize LxApps first
            macOSLxApp.initializeLxAppsIfNeeded()

            // Check if initialization was successful
            if macOSLxApp.isInitialized {

                // Open home LxApp using dynamic configuration
                print("DEBUG: AppDelegate: Opening home LxApp window...")
                macOSLxApp.openHomeLxApp()
            } else {
                print("DEBUG: AppDelegate: ❌ Failed to initialize LxApps system")
                NSApp.terminate(nil)
            }
        }
    }

    func applicationWillTerminate(_ aNotification: Notification) {
        print("DEBUG: AppDelegate: applicationWillTerminate called")
        // Clean up resources
    }

    func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
        return true
    }

    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return true
    }
}

// Parse command line arguments for window size selection
@MainActor
func parseCommandLineArguments() {
    let arguments = CommandLine.arguments
    
    // Look for --size argument
    if let sizeIndex = arguments.firstIndex(of: "--size"),
       sizeIndex + 1 < arguments.count {
        let sizeValue = arguments[sizeIndex + 1].lowercased()
        if let deviceSize = DeviceSize(rawValue: sizeValue) {
            AppConfig.selectedDeviceSize = deviceSize
        } else {
            print("Invalid size option: \(sizeValue)")
            printUsage()
        }
    }
    
    // Look for --help argument
    if arguments.contains("--help") || arguments.contains("-h") {
        printUsage()
        exit(0)
    }
}

func printUsage() {
    print("LingXia Demo App - Window Size Options")
    print("Usage: LingXiaDemo [--size <device>] [--help]")
    print("")
    print("Options:")
    print("  --size <device>  Set window size (default: iphone)")
    print("                   Available devices:")
    for device in DeviceSize.allCases {
        print("                     \(device.rawValue) - \(device.description)")
    }
    print("  --help, -h       Show this help message")
    print("")
    print("Examples:")
    print("  LingXiaDemo --size iphone")
    print("  LingXiaDemo --size ipadpro11")
}

// Main entry point
parseCommandLineArguments()

let app = NSApplication.shared
let delegate = AppDelegate()
app.delegate = delegate
app.run()
