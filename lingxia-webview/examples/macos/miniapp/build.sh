#!/bin/bash
set -e

# Function to handle errors
handle_error() {
    echo "Error: Build failed at line $1"
    exit 1
}

# Set error trap
trap 'handle_error $LINENO' ERR

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

#echo "Cleaning previous build..."
#rm -rf .build

echo "Building for debugging..."
if [ "$(uname -m)" = "arm64" ]; then
    # For Apple Silicon Macs
    swift build --arch arm64
else
    # For Intel Macs
    swift build 
fi

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "Build successful!"

    # Determine the correct binary path
    if [ "$(uname -m)" = "arm64" ]; then
        BINARY_PATH="./.build/arm64-apple-macosx/debug/LingXiaDemo"
    else
        BINARY_PATH="./.build/x86_64-apple-macosx/debug/LingXiaDemo"
    fi

    # Check if binary exists
    if [ -f "$BINARY_PATH" ]; then
        # Create app bundle
        echo "Creating app bundle..."
        if [ "$(uname -m)" = "arm64" ]; then
            BUILD_DIR="./.build/arm64-apple-macosx/debug"
        else
            BUILD_DIR="./.build/x86_64-apple-macosx/debug"
        fi

        APP_BUNDLE="$BUILD_DIR/LingXiaDemo.app"
        rm -rf "$APP_BUNDLE"
        mkdir -p "$APP_BUNDLE/Contents/MacOS"
        mkdir -p "$APP_BUNDLE/Contents/Resources"

        # Copy executable
        cp "$BINARY_PATH" "$APP_BUNDLE/Contents/MacOS/"

        # Copy Info.plist
        cp "Info.plist" "$APP_BUNDLE/Contents/"

        # Copy app icon if it exists
        if [ -f "Sources/Resources/AppIcon.png" ]; then
            cp "Sources/Resources/AppIcon.png" "$APP_BUNDLE/Contents/Resources/"
        fi

        # Copy other resources
        if [ -d "$BUILD_DIR/LingXiaDemo_LingXiaDemo.bundle/Resources" ]; then
            cp -r "$BUILD_DIR/LingXiaDemo_LingXiaDemo.bundle/Resources"/* "$APP_BUNDLE/Contents/Resources/"
        fi

        echo "App bundle created at $APP_BUNDLE"
        echo "Starting LingXiaDemo..."
        # Check for command line arguments and pass them to the app
        if [ $# -gt 0 ]; then
            "$APP_BUNDLE/Contents/MacOS/LingXiaDemo" "$@"
        else
            "$APP_BUNDLE/Contents/MacOS/LingXiaDemo"
        fi

        # Wait a moment for the app to start
        sleep 2

        # Check if the app is running
        if pgrep -f "LingXiaDemo" > /dev/null; then
            echo "✅ LingXiaDemo is running"
        else
            echo "❌ LingXiaDemo failed to start or exited immediately"
        fi
    else
        echo "Error: Binary not found at $BINARY_PATH"
        exit 1
    fi
else
    echo "Error: Build failed"
    exit 1
fi
